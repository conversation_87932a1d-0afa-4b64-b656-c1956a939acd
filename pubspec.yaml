name: driver
description: "A new Flutter project."

# Prevent accidental publishing to pub.dev.
publish_to: 'none'

version: 1.0.0+15

environment:
  sdk: ^3.6.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  lsapp:
    path: ../../../lsapp/lib

  firebase_remote_config: ^5.1.5
  firebase_auth: ^5.3.3
  firebase_app_check: ^0.3.1+6
  firebase_core: ^3.8.0
  cloud_firestore: ^5.6.5
  firebase_storage: ^12.4.10
  cloud_functions: ^5.6.2

  bot_toast: ^4.1.3
  flutter_easyloading: ^3.0.5
  shared_preferences: ^2.5.3
  webview_flutter: ^4.13.0
  event_bus: ^2.0.1
  provider: ^6.1.5
  firebase_cached_image: ^0.8.0
  file_picker: ^10.3.1
  image_picker: ^1.1.2
  url_launcher: ^6.3.2
  # TODO: Upgrade to version ^14.0.0 or higher after migrating to Flutter 3.29.0 or later.
  geolocator: ^13.0.4
  app_settings: ^6.1.1
  flutter_keyboard_visibility: ^6.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  # Enable generation of localized Strings from arb files.
  generate: true

  assets:
    - assets/images/
    - assets/images/country/
    - assets/images/truck/
