import 'dart:async';
import 'package:driver/src/app.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/notification.dart' as notif;
import 'package:driver/src/settings/controller.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/functions.dart';
import 'package:driver/src/widgets/dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:lsapp/lsapp.dart';
import 'package:driver/src/utils/firestore.dart' as fs;

class User extends ChangeNotifier {
  static final User _shared = User();
  static User get shared => _shared;

  late auth.User? user;

  String? fcmToken;
  static const String appleAuthProviderId = 'apple.com';
  static const String emailAuthProviderId = 'password';

  DriverVerification? driverVerification;
  List<TruckVerification> truckVerifications = [];

  void Function()? truckAddedListener;

  Future init() async {
    user = auth.FirebaseAuth.instance.currentUser;
    updateUserInfo();

    auth.FirebaseAuth.instance.userChanges().listen((u) async {
      debugPrint('userChanges $u');
      bool shouldUpdate = false;
      if (user?.uid != u?.uid) {
        shouldUpdate = true;
      }
      user = u;
      debugPrint('shouldUpdate $shouldUpdate');
      if (shouldUpdate) {
        updateUserInfo();
      }
    });
  }

  Future updateUserInfo() async {
    if (hasRegistered()) {
      bool? hasDeleted =
          await Firestore.shared.userData(user!.uid, field: 'deleted');
      debugPrint('hasDeleted $hasDeleted');
      if (hasDeleted == true) {
        await performLogout();
        BuildContext context = MyApp.materialKey.currentContext!;
        if (context.mounted) {
          showAppDialog(context,
              dialogMessage(message: appLoc(context).accountDeletedMessage), [
            dialogButton(
                context, appLoc(context).understand, DialogButtonType.primary)
          ]);
        }
        return;
      }

      await updateAccountInfo();
      await registerFcmToken();
      notif.Notification.shared.update();
    }
  }

  Future reload() async {
    await auth.FirebaseAuth.instance.currentUser?.reload();
  }

  void updateDriverVerification(Map<String, dynamic>? driverVerificationData) {
    if (driverVerificationData != null) {
      driverVerification = DriverVerification.fromMap(driverVerificationData);
      notifyListeners();
    }
  }

  void updateTruckVerification(Map<String, dynamic>? truckVerificationData) {
    if (truckVerificationData != null) {
      truckVerifications.add(TruckVerification.fromMap(truckVerificationData));
      truckAddedListener?.call();
      notifyListeners();
    }
  }

  Future<bool> removeTruckVerification(
      TruckVerification truckVerification) async {
    if (await Functions.shared.deleteTruckVerification(truckVerification)) {
      truckVerifications.remove(truckVerification);
      SettingsController.shared.updateDefaultTruckIndex(0);
      notifyListeners();
      return true;
    }
    return false;
  }

  Future syncDriverVerification() async {
    if (hasRegistered()) {
      updateDriverVerification(
          await fs.Firestore.shared.getDriverVerification());
    }
  }

  Future syncTruckVerifications() async {
    if (hasRegistered()) {
      List<Map<String, dynamic>> res =
          await fs.Firestore.shared.getTruckVerifications();
      truckVerifications = [];
      if (res.isNotEmpty) {
        for (final r in res) {
          updateTruckVerification(r);
        }
      }
    }
  }

  Future updateAccountInfo() async {
    debugPrint('updateAccountInfo');
    await Firestore.shared.updateUser(user!.uid, {
      'email': user!.email,
      'locale': appLoc(MyApp.materialKey.currentContext!).localeName
    });
    await syncDriverVerification();
    syncTruckVerifications();
    notifyListeners();
  }

  String? unverifiedDriverStatus() {
    if (driverVerification == null) {
      return appLoc(MyApp.materialKey.currentContext!).verificationEmpty;
    }
    if (driverVerification?.status == VerificationStatus.verified) {
      return null;
    }
    return driverVerification?.status == VerificationStatus.pending
        ? appLoc(MyApp.materialKey.currentContext!).verificationPending
        : appLoc(MyApp.materialKey.currentContext!).verificationRejected;
  }

  List<TruckVerification> get verifiedTrucks => truckVerifications
      .where((e) => e.status == VerificationStatus.verified)
      .toList();

  TruckVerification? defaultTruck() {
    if (verifiedTrucks.length > SettingsController.shared.defaultTruckIndex) {
      return verifiedTrucks[SettingsController.shared.defaultTruckIndex];
    }
    if (verifiedTrucks.isNotEmpty) {
      SettingsController.shared.updateDefaultTruckIndex(0);
      return verifiedTrucks.first;
    }
    return null;
  }

  void updateDefaultTruck(TruckVerification truck) {
    SettingsController.shared
        .updateDefaultTruckIndex(truckVerifications.indexOf(truck));
    notifyListeners();
  }

  bool hasRegistered() {
    return user?.uid != null;
  }

  bool canViewOrder() {
    return driverVerification?.status == VerificationStatus.verified &&
        truckVerifications
            .where((e) => e.status == VerificationStatus.verified)
            .isNotEmpty;
  }

  Future registerFcmToken() async {
    if (fcmToken != null && hasRegistered()) {
      Firestore.shared.updateMessagingToken(fcmToken!, user!.uid);
    }
  }

  Future logout() async {
    await auth.FirebaseAuth.instance.signOut();
    await signOutGoogle();
  }

  bool loggingOut = false;

  Future<bool> performLogout() async {
    if (loggingOut) {
      return false;
    }
    loggingOut = true;
    Loader.show();

    await User.shared.logout();
    SettingsController.shared.updateSelectedDestCountry(null);
    SettingsController.shared.updateDefaultTruckIndex(0);
    fcmToken = null;
    driverVerification = null;
    truckVerifications = [];
    notifyListeners();
    Loader.hide();
    loggingOut = false;
    return true;
  }

  Future deleteAccount() async {
    Loader.show();
    debugPrint('deleteAccount');
    await Firestore.shared.updateUser(user!.uid, {
      'deleted': true,
    });
    await performLogout();
    notifyListeners();
  }
}
