import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:driver/src/model/selectable.dart';

class CityQuote {
  final String destination;
  final int quotation;
  final String type;
  final String typeName;
  final DateTime updated;
  final DateTime created;
  final double delta;

  CityQuote(
      {required this.destination,
      required this.quotation,
      required this.type,
      required this.typeName,
      required this.updated,
      required this.created,
      required this.delta});
}

DateTime? dateTimeFromMap(Map<String, dynamic> map, String key) {
  if (map[key] == null) return null;
  if (map[key].runtimeType == Timestamp) {
    return (map[key] as Timestamp).toDate();
  } else {
    return Timestamp(map[key]['_seconds'], map[key]['_nanoseconds']).toDate();
  }
}

class Order {
  final String orderId;
  final DateTime updatedAt;
  final String status;
  final String origin;
  final String pickupAddress;
  final DateTime expectedLoadingDate;
  final String destinationCountry;
  final String destinationCity;
  final String destinationAddress;
  final int distance;
  final String goods;
  final String recordId;
  final int targetPrice;
  final int? platformPrice;
  final int? actualCost;
  final String type;
  final List<String> tonnage;
  final List<String> volumes;
  final String? driverId;
  final bool isInvalid;
  final String? weightPhoto;
  final DateTime? loadingStart;
  final DateTime? loadingEnd;
  final DateTime? unloadingStart;
  final DateTime? unloadingEnd;

  Order(
      {required this.orderId,
      required this.updatedAt,
      required this.status,
      required this.origin,
      required this.pickupAddress,
      required this.expectedLoadingDate,
      required this.destinationCountry,
      required this.destinationCity,
      required this.destinationAddress,
      required this.distance,
      required this.goods,
      required this.recordId,
      required this.targetPrice,
      required this.type,
      required this.tonnage,
      required this.volumes,
      this.driverId,
      this.actualCost,
      this.platformPrice,
      this.loadingStart,
      this.loadingEnd,
      this.weightPhoto,
      this.unloadingStart,
      this.unloadingEnd,
      this.isInvalid = false});

  static Order fromMap(Map<String, dynamic> map, {bool isInvalid = false}) {
    return Order(
        orderId: map['orderId'],
        updatedAt: dateTimeFromMap(map, 'updatedAt')!,
        status: map['status'],
        origin: map['origin'],
        pickupAddress: map['pickupAddress'],
        expectedLoadingDate: dateTimeFromMap(map, 'expectedLoadingDate')!,
        destinationCountry: map['destinationCountry'],
        destinationCity: map['destinationCity'],
        destinationAddress: map['destinationAddress'],
        distance: map['distance'],
        goods: map['goods'],
        recordId: map['recordId'],
        targetPrice: map['targetPrice'],
        type: map['type'],
        tonnage:
            map['tonnage'] != null ? List<String>.from(map['tonnage']) : [],
        volumes:
            map['volumes'] != null ? List<String>.from(map['volumes']) : [],
        driverId: map['driverId'],
        actualCost: map['actualCost'],
        platformPrice: map['platformPrice'],
        loadingStart: dateTimeFromMap(map, 'loadingStart'),
        loadingEnd: dateTimeFromMap(map, 'loadingEnd'),
        unloadingStart: dateTimeFromMap(map, 'unloadingStart'),
        unloadingEnd: dateTimeFromMap(map, 'unloadingEnd'),
        weightPhoto: map['weightPhoto'],
        isInvalid: isInvalid);
  }

  int price() {
    return platformPrice ?? 0;
  }
}

abstract class SelectableModel with Selectable {
  final String id;
  final String value;
  final String displayName;
  final String recordId;

  SelectableModel(
      {required this.id,
      required this.value,
      required this.displayName,
      required this.recordId});

  String token();
  String? storage();

  @override
  String identifier() {
    return id;
  }

  @override
  String label() {
    return displayName;
  }
}

class Driver extends SelectableModel {
  final String passportImgToken;
  String? passportImgStorage;
  final String nameEn;

  Driver(
      {required super.recordId,
      required this.passportImgToken,
      this.passportImgStorage,
      required this.nameEn,
      required super.id})
      : super(value: nameEn, displayName: nameEn);

  static Driver fromMap(Map<String, dynamic> map) {
    return Driver(
        recordId: map['recordId'],
        passportImgToken: map['passportImgToken'],
        passportImgStorage: map['passportImgStorage'],
        nameEn: map['nameEn'],
        id: map['id']);
  }

  @override
  String token() => passportImgToken;

  @override
  String? storage() => passportImgStorage;
}

class Truck extends SelectableModel {
  final String license1;
  final String license1ImgToken;
  String? license1ImgStorage;
  final String tonnage;
  final String type;
  final String volume;
  final String? license2;

  Truck(
      {required super.recordId,
      required this.license1,
      required this.license1ImgToken,
      this.license1ImgStorage,
      this.license2,
      required this.tonnage,
      required this.type,
      required this.volume,
      required super.id})
      : super(
            value: license1,
            displayName:
                '$license1${license2?.isNotEmpty == true ? '+$license2' : ''} - $type');

  static Truck fromMap(Map<String, dynamic> map) {
    return Truck(
        recordId: map['recordId'],
        license1: map['license1'],
        license1ImgToken: map['license1ImgToken'],
        license1ImgStorage: map['license1ImgStorage'],
        license2: map['license2'],
        type: map['type'],
        tonnage: map['tonnage'] ?? '',
        volume: map['volume'] ?? '',
        id: map['id']);
  }

  @override
  String token() => license1ImgToken;

  @override
  String? storage() => license1ImgStorage;
}

enum VerificationStatus { pending, verified, rejected }

class DriverVerification {
  final String uid;
  final String phone;
  final String name;
  final String passport;
  final VerificationStatus status;
  final String? driverId;
  final String? chinesePhone;
  final String? wechat;
  final String? reason;

  DriverVerification(
      {required this.uid,
      required this.phone,
      required this.name,
      required this.passport,
      required this.status,
      required this.driverId,
      required this.chinesePhone,
      required this.wechat,
      this.reason});

  static DriverVerification fromMap(Map<String, dynamic> map) {
    return DriverVerification(
        uid: map['uid'],
        phone: map['phone'],
        name: map['name'],
        passport: map['passport'],
        status: VerificationStatus.values.byName(map['status']),
        driverId: map['driverId'],
        chinesePhone: map['chinesePhone'],
        wechat: map['wechat'],
        reason: map['reason']);
  }
}

class TruckVerification with Selectable {
  final String id;
  final String uid;
  final String license;
  final String licenseStorage;
  final String type;
  final String tonnage;
  final String volume;
  final String? truckId;
  final VerificationStatus status;
  final String? reason;
  final String? recordId;

  TruckVerification(
      {required this.id,
      required this.uid,
      required this.license,
      required this.licenseStorage,
      required this.type,
      required this.status,
      required this.tonnage,
      required this.volume,
      required this.truckId,
      this.reason,
      this.recordId});

  static TruckVerification fromMap(Map<String, dynamic> map) {
    return TruckVerification(
        id: map['id'],
        uid: map['uid'],
        license: map['license'],
        licenseStorage: map['licenseStorage'],
        type: map['type'],
        status: VerificationStatus.values.byName(map['status']),
        tonnage: map['tonnage'],
        volume: map['volume'],
        truckId: map['truckId'],
        reason: map['reason'],
        recordId: map['recordId']);
  }

  @override
  String identifier() {
    return id;
  }

  @override
  String label() {
    return '$license - $type';
  }
}

enum BankType { individual, business }

class BankInfo {
  final BankType type;
  final String? uid;
  final String? id;
  final String? recordId;

  BankInfo({this.id, required this.type, this.uid, this.recordId});

  static BankInfo fromMap(Map<String, dynamic> map) {
    if (map['type'] == BankType.individual.name) {
      return IndividualBankInfo(
          name: map['name'],
          account: map['account'],
          bank: map['bank'],
          id: map['id'],
          uid: map['uid'],
          recordId: map['recordId']);
    } else {
      return BusinessBankInfo(
          name: map['name'],
          account: map['account'],
          bank: map['bank'],
          swift: map['swift'],
          taxNumber: map['taxNumber'],
          address: map['address'],
          beneficiaryCode: map['beneficiaryCode'],
          usageCode: map['usageCode'],
          id: map['id'],
          uid: map['uid'],
          recordId: map['recordId']);
    }
  }
}

class IndividualBankInfo extends BankInfo {
  final String name;
  final String account;
  final String bank;

  IndividualBankInfo(
      {required this.name,
      required this.account,
      required this.bank,
      super.id,
      super.uid,
      super.recordId})
      : super(type: BankType.individual);
}

class BusinessBankInfo extends BankInfo {
  final String bank;
  final String swift;
  final String account;
  final String name;
  final String taxNumber;
  final String address;
  final String beneficiaryCode;
  final String usageCode;

  BusinessBankInfo(
      {required this.name,
      required this.account,
      required this.bank,
      required this.swift,
      required this.taxNumber,
      required this.address,
      required this.beneficiaryCode,
      required this.usageCode,
      super.id,
      super.uid,
      super.recordId})
      : super(type: BankType.business);
}

class OrderNegotiation {
  final String id;
  final String driverId;
  final String orderId;
  final int quotation;
  final String truckId;
  final String orderRecordId;
  final String uid;

  OrderNegotiation(
      {required this.id,
      required this.driverId,
      required this.orderId,
      required this.quotation,
      required this.truckId,
      required this.orderRecordId,
      required this.uid});

  static OrderNegotiation fromMap(String id, Map<String, dynamic> map) {
    return OrderNegotiation(
        id: id,
        driverId: map['driverId'],
        orderId: map['orderId'],
        quotation: map['quotation'],
        truckId: map['truckId'],
        orderRecordId: map['orderRecordId'],
        uid: map['uid']);
  }
}

class Notification {
  final String id;
  final DateTime created;
  final bool unread;
  final String? routeName;
  final String type;
  final String title;
  final String body;
  final Object? arguments;

  Notification(
      {required this.id,
      required this.created,
      required this.unread,
      required this.routeName,
      required this.type,
      required this.title,
      required this.body,
      this.arguments});

  static Notification fromMap(Map<String, dynamic> map) {
    return Notification(
        id: map['id'],
        created: (map['created'] as Timestamp).toDate(),
        unread: map['unread'],
        routeName: map['message']['data']['routeName'],
        type: map['message']['data']['type'],
        title: map['message']['notification']['title'],
        body: map['message']['notification']['body'],
        arguments: map['message']['data']['arguments']);
  }
}
