import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Kazakh (`kk`).
class AppLocalizationsKk extends AppLocalizations {
  AppLocalizationsKk([String locale = 'kk']) : super(locale);

  @override
  String get appTitle => 'GoFreight';

  @override
  String get welcome => 'келулеріңізді қарсы аламыз ';

  @override
  String get use => 'пайдалануларыңызды қарсы аламыз ';

  @override
  String get googleLogin => 'Google ұқық беріп тізімделу';

  @override
  String get appleLogin => 'Apple ұқық беріп тізімделу';

  @override
  String get agreementText => 'мен ';

  @override
  String get userAgreement => '«машина бағдарламалық құралдан пайдалану келісімін»、';

  @override
  String get privacyAgreement => '«машина бағдарламалық құралдың жеке құпиалығы келісімін»、';

  @override
  String get infoAgreement => '«машина информатсиаға уәде беру келісімін»';

  @override
  String get agreementText1 => ' оқып шықтым және оған қосыламын.';

  @override
  String get contactUs => 'мәселеге жолыққанда бізбен хабарласайық';

  @override
  String get notAgreeMessage1 => 'келісімді әлі егжей-тегжейлі оқымағаныңызды тексеріп көріңіз, ';

  @override
  String get notAgreeMessage2 => ' мұқиат оқып шығыңыз, біздің өнімдеріміз бен қізмет өтеуімізді «мақұлдауды» бастаңыз. ';

  @override
  String get cancel => 'күшінен қалдыру';

  @override
  String get agree => 'қосылу';

  @override
  String get readAgreement => 'келісімді оқыңыз';

  @override
  String get home => 'бас бет';

  @override
  String get order => 'Заказ';

  @override
  String get mine => 'өзім';

  @override
  String get networkError => 'интернет жақсы емес，сәлден соң сынап көріңіз';

  @override
  String loadFrom(Object origin) {
    return 'қытай $origin жүк тиеу';
  }

  @override
  String get selectDestinationCountry => 'бармақшы болған мемлекетті таңдаңыз';

  @override
  String get loadingTruck => 'тасымал автомобилы: ';

  @override
  String get loadingTruckNotRegistered => 'сіз көлік информатсиасын әлі қорғамадыңыз , барасыз';

  @override
  String get registerTruck => 'автомобил информатсиасын кемелдендіру，марқамет ';

  @override
  String get loadingTruckNotVerified => ' ';

  @override
  String get quoteCenter => 'базар жағдайы орталығы';

  @override
  String quoteFrom(Object origin) {
    return '$origin жолға шықты';
  }

  @override
  String get rankByPrice => 'жоғары бағада ретке тұрғызу';

  @override
  String get rankByPref => 'әуестену тәртібі';

  @override
  String get pickupDate => 'жүк тиеу уақыты:';

  @override
  String distance(Object distance) {
    return 'жалпы аралығы ${distance}km';
  }

  @override
  String get loading => 'жүк тиеу';

  @override
  String get unloading => 'жүк түсіру';

  @override
  String get noOrder => 'қәзірше зәкәз жоқ';

  @override
  String get noData => 'қәзірше дерек жоқ';

  @override
  String get viewOrder => 'тапсырысты тексеру';

  @override
  String get rankByPrefNotice => 'сіз aлдімен жоғары жақта жүк тиеу-түсіру орны мен тасымал автомобилын қорғауыңыз керек, сонда ғана әуестенуіңізге болады';

  @override
  String get login => 'тіркелу';

  @override
  String get verifyTitle => 'шөфер текшені куәләндіріп тез арада Заказ тапсырып алды';

  @override
  String get verifyAction => 'куәләндіруғә бару';

  @override
  String get massiveOrders => 'қыруар зәкәз';

  @override
  String get fastPay => 'дер кезінде зәкәз беру';

  @override
  String get safe => 'текше кепілдігі';

  @override
  String get driverCenter => 'шөфер орталығы';

  @override
  String get driverVerify => 'шөферлік куәлігі';

  @override
  String get truckManagement => 'көлік басқару';

  @override
  String get bankInfo => 'бәнке информатсиасы';

  @override
  String get serviceCenter => 'қызмет ету орталығы';

  @override
  String get customerService => 'клиенттік қолдау қызметімен хабарластыңыз';

  @override
  String get guarantee => 'информатсиалық уәде қаттамасы';

  @override
  String get userAgreementAction => 'тұтынушылар келісімі';

  @override
  String get privacyAgreementAction => 'құпиа келісім';

  @override
  String get switchLanguage => 'тіл ауыстыру';

  @override
  String get completeInfo => 'тіркелу';

  @override
  String get userInfo => 'жеке информатсиа';

  @override
  String get phone => 'телефон';

  @override
  String get phonePlaceholder => 'телефөніңізді кіргізіңіз';

  @override
  String get driverName => 'шөфердің әті-жөні (ағылшынша) ';

  @override
  String get driverNamePlaceholder => 'әті-жөніңізді енгізіңіз';

  @override
  String get passportPhoto => 'паспорттың суреті';

  @override
  String get moreContactInfo => 'тіпті де көп байланыс тәсілі ';

  @override
  String get chinesePhone => 'қытай телефон нөмірі';

  @override
  String get chinesePhonePlaceholder => 'қытай телефөн нөміріңізді кіргізіңіз.';

  @override
  String get wechat => 'WeChat нөмірі';

  @override
  String get wechatPlaceholder => 'WeChat нөміріңізді кіргізіңіз';

  @override
  String get save => 'сақтау';

  @override
  String get quickEntry => 'тез кіргізу';

  @override
  String get quickEntryMessage => 'толтырған мазмұныңызға негізделіп , төмендегі информатсиаларға сәйкеседі , дұрыс информатсианы таңдап тез толтыруыңызға болады';

  @override
  String get quickEntryNotAvailable => 'төмендегі информатсиаларды жалғасты толтырыңыз';

  @override
  String get confirmSelect => 'анықтап сұрыптау';

  @override
  String get cancelSelect => 'барлығы емес';

  @override
  String get imageFromAlbum => 'албом арқылы сұрыптау';

  @override
  String get imageFromCamera => 'суретке түсіру';

  @override
  String get allQuoteTruckTypes => 'барлық машина түрі';

  @override
  String get allQuoteAreas => 'барлық жер';

  @override
  String get latestQuote => 'ең жаңа баға';

  @override
  String get quoteDelta => 'күндік құлдырау салыстырмасы';

  @override
  String get quoteDetail => 'базар жағдайы орталығы';

  @override
  String get quoteHistory => 'тарихи баға';

  @override
  String get quoteTime => 'бағаны мәлімдеу уақыты';

  @override
  String get quotePrice => 'баға';

  @override
  String get waitForDriverVerificationTitle => 'тексеруге тапсыру';

  @override
  String get waitForDriverVerificationMessage => 'сіз тексеруді сәтті тапсырдыңыз, біз 3 қізмет күні ішінде тексеріп боламыз, осы мезгілде телефөннің іркіліссіздігін сақтаңыз. жалғасты көлік куәлендіресіз бе？';

  @override
  String get waitForTruckVerificationMessage => 'сіз тексеруді сәтті тапсырдыңыз, біз 3 қізмет күні ішінде тексеріп боламыз, осы мезгілде телефөннің іркіліссіздігін сақтаңыз，тексеріп мақұлдағаннан кейін дереу заказ қабылдауға болады。';

  @override
  String get notYet => 'қазыр толтырмаймыз';

  @override
  String get goVerify => 'дереу анықтаy';

  @override
  String get understand => 'білдім';

  @override
  String get noTruck => 'уақытша машина жоқ，тыңнан ';

  @override
  String get add => 'көбейту';

  @override
  String get addTruckTitle => 'жаңадан қосылған автомобил';

  @override
  String get license => 'автомобил нөмірі';

  @override
  String get licensePlaceholder => 'автомобил нөмірін кіргізіңіз';

  @override
  String get licenseImage => 'көлік қатынау куәлігі';

  @override
  String get next => 'келесі қадам';

  @override
  String get truckType => 'автомобил түрі';

  @override
  String get tonnage => 'әвтөмөбилге тиелген тонналық саны';

  @override
  String get tonnagePlaceholder => 'тоннасын таңдаңыз';

  @override
  String get volume => 'әвтөмөбилге тиелген тонналық саны';

  @override
  String get volumePlaceholder => 'көлемін таңдаңыз';

  @override
  String get confirm => 'тұрақтандыру';

  @override
  String get verified => 'куәлeндірілғeн';

  @override
  String get verificationEmpty => 'куәлeндіріләтін';

  @override
  String get verificationPending => 'куәлeндіру барысында';

  @override
  String get verificationRejected => 'куәлeндіру сәтсіз болу';

  @override
  String get verificationRejectedReason => 'клиенттік қолдау қызметімен хабарластыңыз';

  @override
  String get deleteTruckMessage => 'сіз қатысты көліктерді өшіріп жатырсыз，өшіруді тұрақтандыру?';

  @override
  String get confirmDelete => 'өшіруді тұрақтандыру';

  @override
  String get cancelDelete => 'өшірмеу';

  @override
  String get noBank => 'қәзірше бәнке информатсиасы жоқ, тыңнан ';

  @override
  String get addBank => 'көбейтіңіз';

  @override
  String get addBankTitle => 'жаңадан артқан бәнке информатсиасы';

  @override
  String get selectBankType => 'есеп түрін таңдау';

  @override
  String get individual => 'жеке адам';

  @override
  String get business => 'жеке өнеркәсіпші-саудагерлер';

  @override
  String get individualName => 'жеке адамның аты-жөні';

  @override
  String get individualNamePlaceholder => 'өзіңіздің әті-жөніңізді жазыңыз';

  @override
  String get bankName => 'есеп ашқан бәнкенің аты';

  @override
  String get bankNamePlaceholder => 'есеп ашқан бәнкенің атын енгізіңіз';

  @override
  String get bankAccount => 'есеп нөмірі';

  @override
  String get bankAccountPlaceholder => 'есеп нөміріңізді кіргізіңіз';

  @override
  String get submit => 'жолдау';

  @override
  String get swiftAccount => 'KZT SWIFT балама нөмір BIC';

  @override
  String get swiftAccountPlaceholder => 'сіздің KZT SWIFT балама нөмір BIC';

  @override
  String get businessOwnerName => 'аты-жөні';

  @override
  String get businessOwnerNamePlaceholder => 'жеке өнеркәсіпші-саудагерлер аты-жөнін енгізіңіз';

  @override
  String get taxNumber => 'бажы нөмірі';

  @override
  String get taxNumberPlaceholder => 'бажы нөміріңізді кіргізіңіз';

  @override
  String get businessAddress => 'тіркелген әдрес';

  @override
  String get businessAddressPlaceholder => 'жеке өнеркәсіпші-саудагерлер тіркелген әдрес';

  @override
  String get beneficiaryCode => 'игіліктенушінің шартты нөмірі';

  @override
  String get beneficiaryCodePlaceholder => 'игіліктенушінің шартты нөмірін кіргізіңіз';

  @override
  String get usageCode => 'ақша төлеудің қолданылу шарт таңбасы';

  @override
  String get usageCodePlaceholder => 'ақша төлеудің істетілу орнын енгізіңіз';

  @override
  String get deleteBankMessage => 'сіз қатысты бәнке информатсиасын өшіріп жатырсыз, өшіруді тұрақтандырдыңыз ба?';

  @override
  String get submitSuccess => 'тапсырылып болу';

  @override
  String get switchTruck => ' ауыстыру';

  @override
  String get loadingImage => 'суретті түсіріп жатыр';

  @override
  String get selectTruck => 'машинаны таңдау';

  @override
  String get uid => 'тұтынушы нөмірі';

  @override
  String get copyToClipboard => 'қиып жапсыру тақтасына көшірілді';

  @override
  String get preventTruckDeleteTitle => 'автомобил информатсиасын куaләндірудә';

  @override
  String get preventTruckDelete => 'көлік информатсиасы куәләндірілудә, сәл кейін жұмыс жүргізіңіз немесе клиенттік қолдау қызметімен байланыс жасап, жедел бір жақты етіңіз。';

  @override
  String get contactForVerification => 'клиенттік қолдау қызметімен байланыс жасап, жедел бір жақты етіңіз';

  @override
  String get notVerifiedDriverTitle => 'әлі куaләндірілмeғән';

  @override
  String get notVerifiedDriverMessage => 'сіздің мәтериәлдәріңіз кемелді емес, қазыр заказ қабылдай алмайсыз, мәтериәлдәріңізді дер кезінде кемелдендіріп, дереу заказ қабылдаңыз';

  @override
  String get notVerifiedTruckTitle => 'уақытша көлік жоқ';

  @override
  String get notVerifiedTruckMessage => 'сіз көлік информатсиасын әлі қорғамадыңыз , қазыр заказ қабылдай алмайсыз , көлік информатсиасын дер кезінде кемелдендіріп , дереу заказ қабылдаңыз';

  @override
  String get verifyingDriverTitle => 'қазыр куaлeндірілудә';

  @override
  String get verifyingDriverMessage => 'сіздің мәтериәлдaріңіз куaлeндірілудә, қазыр заказ қабылдауға болмайды, клиенттік қолдау қызметімен байланыс жасап, куәләндіруді тездетуіңізге болады';

  @override
  String get verifyingTruckTitle => 'көлік куaлeндіруіндe';

  @override
  String get verifyingTruckMessage => 'сіздің автомобил информатсиасын қорғауыңыз куaлeндірілудe，қазыр заказ қабылдауға болмайды, клиенттік қолдау қызметімен байланыс жасап, куәләндіруді тездетуіңізге болады';

  @override
  String get orderDetail => 'тапсырыс егжей-тегжейлі';

  @override
  String get orderDistance => 'тапсырыстың жалпы аралығы';

  @override
  String get orderLoadingAddress => 'жүк тиеу орны';

  @override
  String get orderUnloadingAddress => 'жүк түсірілетін жер';

  @override
  String get orderLoadingTime => 'жүк тиеу уақыты';

  @override
  String get orderCost => 'тасымал ақысы';

  @override
  String get makeQuote => 'бағаны мәлімдеу жөнінде кеңесу';

  @override
  String get placeOrder => 'дереу Заказ қабылдау';

  @override
  String get orderManagement => 'тапсырыс бойынша басқару';

  @override
  String get orderStatusMatching => 'келістіруде';

  @override
  String get orderStatusOngoing => 'жүргізіліп жатыр';

  @override
  String get orderStatusFinished => 'айақталды';

  @override
  String get orderStatusCanceled => 'күшінен қалдырылды';

  @override
  String get allCities => 'барлық жер';

  @override
  String get allPrices => 'толық баға';

  @override
  String get priceAbove => ' нан жоғары';

  @override
  String get customerServiceWithWhatsapp => 'жақын арада WhatsApp клиенттік қолдау қызметі жақын арада WhatsApp арқылы хабарласады';

  @override
  String get customerServiceWithPhone => 'телефөн арқылы клиенттік қолдау қызметімен байланыс жасайды';

  @override
  String get contact => 'хабарласу';

  @override
  String get orderTargetPrice => 'зат иесінің қәзіргі бағасы';

  @override
  String get confirmQuote => 'тұрақтандырылған баға';

  @override
  String get cancelQuote => 'бағаны күшінен қалдыру';

  @override
  String get selectYourTruck => 'машинаңызды таңдаңыз';

  @override
  String get enterYourQuote => 'бағаңызды кіргізіңіз';

  @override
  String get operationSuccess => 'сәтті меңгерілді';

  @override
  String get operationFailed => 'меңгеру сәтсіз , сәлден соң қайта сынап көріңіз';

  @override
  String get quoteRequired => 'бағаңызды кіргізіңіз';

  @override
  String get confirmPlaceOrder => 'зaкaзді тұрақтандыру';

  @override
  String get cancelPlaceOrder => 'зaкaзды күшінен қалдыру';

  @override
  String get placingOrder => 'тапсырысты бір жайлы етіп жатырмыз , сәлден тосыңыз';

  @override
  String get notifications => 'хабар';

  @override
  String get statusFindingSubtitle => 'зат иесі жаңа тапсырыс жариалады';

  @override
  String get statusMatchingSubtitle => 'жүйе қазыр сұрыптап сәйкестіруде';

  @override
  String get statusPendingConfirmSubtitle => 'зaкaз тапсырып алып , зат иесінің тұрақтандыруын күт';

  @override
  String get statusPendingContractSubtitle => 'тоқтамға қол қойылу үстінде, кез келген уақытта телефөнғә назар аудару керек';

  @override
  String get statusPendingLoadSubtitle => 'зaкaз сәтті қабылданып , тез арада зат тиейтін жерге барып зат тиейік';

  @override
  String get statusQueueingSubtitle => 'қатарға тұрып , сәбірліліқпен күтті';

  @override
  String get statusTransportSubtitle => 'тасымалдай бастады , әр күні тізімделген орын есіңде болсын';

  @override
  String get statusInClearanceSubtitle => 'қатарға тұрып кеденді тазалау барысында сәбірліліқпен күтті';

  @override
  String get statusHeadingDestinationSubtitle => 'көздеген жерге баруда , тасымал барысында мәселе болса естелікке мәлімдеңіз';

  @override
  String get statusPendingUnloadSubtitle => 'көздеген жерге жетті , затты дер кезінде түсіріңіз';

  @override
  String get statusCompleteSubtitle => 'тапсырыс орындалып болды, жол бойы қорғап келгеніңізге алғыс айтамын';

  @override
  String get statusCancelledSubtitle => 'тапсырыс күшінен қалдырылды , егер мәселе болса байланыс жасаңыз';

  @override
  String get contactService => 'байланыс жасау';

  @override
  String get orderInvalidStatus => 'күшін жойу';

  @override
  String get logs => 'күнделік';

  @override
  String get weightPhoto => 'таразылауды фотаға түсіру';

  @override
  String get weightPhotoPlaceholder => 'таразыдан өткен суретті жолдаңыз';

  @override
  String get finishLoad => 'жүк тиеу айақталды';

  @override
  String get activityReport => 'оқиғаны тізімге алдыру';

  @override
  String get locationNotAvailableTitle => 'орнын тұрақтандырудың сәтсіз болуы';

  @override
  String get locationNotAvailableMessage => 'сіздің орныңызға ие болып, тасымал зәкәзін бір жайлы етуге тура келеді, сіздің орын белгілеу ұқық шегіңіздің басталған-ашылмағандығын тексеріңіз';

  @override
  String get openSettings => 'ашып орналастыру';

  @override
  String get acquiringLocation => 'орын белгілеп жатыр';

  @override
  String get viewPayment => 'дөкументті тексеру';

  @override
  String get temporaryStay => 'уақыттық демәлу';

  @override
  String get temporaryStayPlaceholder => 'демәлістің себебін кіргізіңіз';

  @override
  String get unexpectedDelay => 'ерекше кешеуілдету';

  @override
  String get unexpectedDelayPlaceholder => 'кешігу себебін кіргізіңіз';

  @override
  String get selectReportItem => 'мәлімдейтін істерді таңдау';

  @override
  String get acquireLocationFailed => 'орын тұрақтандыру сәтсіз , сәлден соң сынап көріңіз';

  @override
  String get dailyReport => 'әр күні тізімге алдыру';

  @override
  String get startClearance => 'кеденді тазалау басталды';

  @override
  String get endClearance => 'кеденді тазалау айақталды';

  @override
  String get arriveDestination => 'көздеген жерге жету';

  @override
  String get unloadComplete => 'жүк түсіріліп болды';

  @override
  String get uploadReceipt => 'өткізіп беру талонын жолдау';

  @override
  String get uploadReceiptPlaceholder => 'өткізіп беру-өткізіп алу талонының суретін жолдаңыз';

  @override
  String get cannotTakeOrderTitle => 'талон ала алмау';

  @override
  String get cannotTakeOrderMessage => 'сізде қазыр жүргізіліп жатқан тапсырыс бар, қәзіргі тапсырысты орындағаннан кейін жаңа тапсырыс қабылдаңыз';

  @override
  String get signOut => 'тізімделуден шегініп шығу';

  @override
  String get signOutMessage => 'сіз есеп нөмірінен шегініп жатырсыз , шегініп шығу-шықпауды тұрақтандырыңыз';

  @override
  String get deleteAccount => 'есеп нөмірін күшінен қалдыру';

  @override
  String get deleteAccountMessage => 'сіз есеп нөмірін күшінен қалдырып жатырсыз , күшінен қалдырылғаннан кейін санды мәліметтер тазаланады , тұрақтандырыңыз';

  @override
  String get accountDeletedMessage => 'есеп нөмірі күшінен қалдырылды , клиенттік қолдау қызметімен  байланыс жасаңыз';

  @override
  String get unauthenticatedError => 'APPкуaлeндіру сәтсіз болса , үкімет арнасынанAPP түсіріңіз';

  @override
  String get onGoingOrder => 'жүргізіліп жатқан тапсырыс';

  @override
  String get orderWeightPhotoMessage => 'тез арада зат тиейтін жерге барып зат тиеіңіз , сөзсіз таразыдан өткенде суретке түсіргеніңіз есіңізде болсын';

  @override
  String get orderFinishLoadMessage => 'сіз машинаға зат тиеп болдыңыз ба , егерде зат тиеп болсаңыз зат тиеп  болдым деген кнөпкaні басыңыз';

  @override
  String get orderStartClearanceMessage => 'сіз кеденге келіп кеденді тазалауыңыз мүмкін , кеденді тазалау басталған-басталмағандығын тұрақтандырыңыз';

  @override
  String get orderEndClearanceMessage => 'сіз кедендік адақтауды орындап болған болуыңыз мүмкін, кедендік адақтауды орындап болған-болмағанын тұрақтандырыңыз';

  @override
  String get orderArriveDestinationMessage => 'сіз зат түсіретін жерге жеткен болуыңыз мүмкін, көздеген жерге жеткен-жетпегендігін тұрақтандыруыңыз керек';

  @override
  String get orderUploadReceiptMessage => 'сіз зат түсіріп болдыңыз , өткізіп беру-өткізіп алу талонын  жолдаңыз';

  @override
  String get orderPositionReportMessage => 'сіздің зaкaзіңіз бар , тізімделу орнын басыңыз';

  @override
  String get reportPosition => 'орнын мәлімдеп тізімге алдыру';

  @override
  String get guestAccess => 'Guest Access';

  @override
  String get reachBottom => '-ең төменгі шекке жетті-';

  @override
  String get notVerifiedCannotViewQuotes => 'сіздің мәтериәлдәріңіз кемелді емес, қазыр базар жағдайының егжей-тегжейлі жағдайын тексеріпбіле алмайсыз， мәтериәлдaріңізді дер кезінде кемелдендіріп алыңыз';
}
