import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_kk.dart';
import 'app_localizations_ru.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'localization/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('kk'),
    Locale('ru'),
    Locale('zh')
  ];

  /// No description provided for @appTitle.
  ///
  /// In zh, this message translates to:
  /// **'GoFreight'**
  String get appTitle;

  /// No description provided for @welcome.
  ///
  /// In zh, this message translates to:
  /// **'欢迎来到'**
  String get welcome;

  /// No description provided for @use.
  ///
  /// In zh, this message translates to:
  /// **'欢迎使用'**
  String get use;

  /// No description provided for @googleLogin.
  ///
  /// In zh, this message translates to:
  /// **'Google授权登录'**
  String get googleLogin;

  /// No description provided for @appleLogin.
  ///
  /// In zh, this message translates to:
  /// **'Apple授权登录'**
  String get appleLogin;

  /// No description provided for @agreementText.
  ///
  /// In zh, this message translates to:
  /// **'我已阅读并同意'**
  String get agreementText;

  /// No description provided for @userAgreement.
  ///
  /// In zh, this message translates to:
  /// **'《GoFreight软件用户协议》、'**
  String get userAgreement;

  /// No description provided for @privacyAgreement.
  ///
  /// In zh, this message translates to:
  /// **'《GoFreight软件隐私协议》、'**
  String get privacyAgreement;

  /// No description provided for @infoAgreement.
  ///
  /// In zh, this message translates to:
  /// **'《GoFreight信息承诺协议》'**
  String get infoAgreement;

  /// No description provided for @agreementText1.
  ///
  /// In zh, this message translates to:
  /// **'。'**
  String get agreementText1;

  /// No description provided for @contactUs.
  ///
  /// In zh, this message translates to:
  /// **'遇见问题，联系我们'**
  String get contactUs;

  /// No description provided for @notAgreeMessage1.
  ///
  /// In zh, this message translates to:
  /// **'检测到您尚未详细阅读协议，请您仔细阅读并同意'**
  String get notAgreeMessage1;

  /// No description provided for @notAgreeMessage2.
  ///
  /// In zh, this message translates to:
  /// **'，请点击“同意”开始使用我们的产品和服务。'**
  String get notAgreeMessage2;

  /// No description provided for @cancel.
  ///
  /// In zh, this message translates to:
  /// **'取消'**
  String get cancel;

  /// No description provided for @agree.
  ///
  /// In zh, this message translates to:
  /// **'同意'**
  String get agree;

  /// No description provided for @readAgreement.
  ///
  /// In zh, this message translates to:
  /// **'阅读协议'**
  String get readAgreement;

  /// No description provided for @home.
  ///
  /// In zh, this message translates to:
  /// **'首页'**
  String get home;

  /// No description provided for @order.
  ///
  /// In zh, this message translates to:
  /// **'订单'**
  String get order;

  /// No description provided for @mine.
  ///
  /// In zh, this message translates to:
  /// **'我的'**
  String get mine;

  /// No description provided for @networkError.
  ///
  /// In zh, this message translates to:
  /// **'网络异常，请稍后再试'**
  String get networkError;

  /// No description provided for @loadFrom.
  ///
  /// In zh, this message translates to:
  /// **'从 {origin} 装车'**
  String loadFrom(Object origin);

  /// No description provided for @selectDestinationCountry.
  ///
  /// In zh, this message translates to:
  /// **'请选择你准备要去的国家'**
  String get selectDestinationCountry;

  /// No description provided for @loadingTruck.
  ///
  /// In zh, this message translates to:
  /// **'运载车辆: '**
  String get loadingTruck;

  /// No description provided for @loadingTruckNotRegistered.
  ///
  /// In zh, this message translates to:
  /// **'您尚未维护车辆信息，去'**
  String get loadingTruckNotRegistered;

  /// No description provided for @registerTruck.
  ///
  /// In zh, this message translates to:
  /// **'完善车辆信息'**
  String get registerTruck;

  /// No description provided for @loadingTruckNotVerified.
  ///
  /// In zh, this message translates to:
  /// **'车辆信息未认证，请'**
  String get loadingTruckNotVerified;

  /// No description provided for @quoteCenter.
  ///
  /// In zh, this message translates to:
  /// **'行情中心'**
  String get quoteCenter;

  /// No description provided for @quoteFrom.
  ///
  /// In zh, this message translates to:
  /// **'{origin}出发'**
  String quoteFrom(Object origin);

  /// No description provided for @rankByPrice.
  ///
  /// In zh, this message translates to:
  /// **'高价排序'**
  String get rankByPrice;

  /// No description provided for @rankByPref.
  ///
  /// In zh, this message translates to:
  /// **'偏好排序'**
  String get rankByPref;

  /// No description provided for @pickupDate.
  ///
  /// In zh, this message translates to:
  /// **'取货日期:'**
  String get pickupDate;

  /// No description provided for @distance.
  ///
  /// In zh, this message translates to:
  /// **'全程{distance}km'**
  String distance(Object distance);

  /// No description provided for @loading.
  ///
  /// In zh, this message translates to:
  /// **'装车'**
  String get loading;

  /// No description provided for @unloading.
  ///
  /// In zh, this message translates to:
  /// **'卸货'**
  String get unloading;

  /// No description provided for @noOrder.
  ///
  /// In zh, this message translates to:
  /// **'暂无订单'**
  String get noOrder;

  /// No description provided for @noData.
  ///
  /// In zh, this message translates to:
  /// **'暂无数据'**
  String get noData;

  /// No description provided for @viewOrder.
  ///
  /// In zh, this message translates to:
  /// **'查看订单'**
  String get viewOrder;

  /// No description provided for @rankByPrefNotice.
  ///
  /// In zh, this message translates to:
  /// **'您需要先在上方维护装车卸车地及运载车辆，才可以设置偏好'**
  String get rankByPrefNotice;

  /// No description provided for @login.
  ///
  /// In zh, this message translates to:
  /// **'登录'**
  String get login;

  /// No description provided for @verifyTitle.
  ///
  /// In zh, this message translates to:
  /// **'认证平台司机，快速接单'**
  String get verifyTitle;

  /// No description provided for @verifyAction.
  ///
  /// In zh, this message translates to:
  /// **'前往认证'**
  String get verifyAction;

  /// No description provided for @massiveOrders.
  ///
  /// In zh, this message translates to:
  /// **'大量订单'**
  String get massiveOrders;

  /// No description provided for @fastPay.
  ///
  /// In zh, this message translates to:
  /// **'及时结算'**
  String get fastPay;

  /// No description provided for @safe.
  ///
  /// In zh, this message translates to:
  /// **'平台保障'**
  String get safe;

  /// No description provided for @driverCenter.
  ///
  /// In zh, this message translates to:
  /// **'司机中心'**
  String get driverCenter;

  /// No description provided for @driverVerify.
  ///
  /// In zh, this message translates to:
  /// **'司机认证'**
  String get driverVerify;

  /// No description provided for @truckManagement.
  ///
  /// In zh, this message translates to:
  /// **'车辆管理'**
  String get truckManagement;

  /// No description provided for @bankInfo.
  ///
  /// In zh, this message translates to:
  /// **'银行信息'**
  String get bankInfo;

  /// No description provided for @serviceCenter.
  ///
  /// In zh, this message translates to:
  /// **'服务中心'**
  String get serviceCenter;

  /// No description provided for @customerService.
  ///
  /// In zh, this message translates to:
  /// **'联系客服'**
  String get customerService;

  /// No description provided for @guarantee.
  ///
  /// In zh, this message translates to:
  /// **'信息承诺协议'**
  String get guarantee;

  /// No description provided for @userAgreementAction.
  ///
  /// In zh, this message translates to:
  /// **'用户协议'**
  String get userAgreementAction;

  /// No description provided for @privacyAgreementAction.
  ///
  /// In zh, this message translates to:
  /// **'隐私协议'**
  String get privacyAgreementAction;

  /// No description provided for @switchLanguage.
  ///
  /// In zh, this message translates to:
  /// **'切换语言'**
  String get switchLanguage;

  /// No description provided for @completeInfo.
  ///
  /// In zh, this message translates to:
  /// **'请完善信息'**
  String get completeInfo;

  /// No description provided for @userInfo.
  ///
  /// In zh, this message translates to:
  /// **'个人信息'**
  String get userInfo;

  /// No description provided for @phone.
  ///
  /// In zh, this message translates to:
  /// **'电话'**
  String get phone;

  /// No description provided for @phonePlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入您的电话'**
  String get phonePlaceholder;

  /// No description provided for @driverName.
  ///
  /// In zh, this message translates to:
  /// **'司机姓名（英语）'**
  String get driverName;

  /// No description provided for @driverNamePlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入您的姓名'**
  String get driverNamePlaceholder;

  /// No description provided for @passportPhoto.
  ///
  /// In zh, this message translates to:
  /// **'护照照片'**
  String get passportPhoto;

  /// No description provided for @moreContactInfo.
  ///
  /// In zh, this message translates to:
  /// **'更多联系方式'**
  String get moreContactInfo;

  /// No description provided for @chinesePhone.
  ///
  /// In zh, this message translates to:
  /// **'中国电话'**
  String get chinesePhone;

  /// No description provided for @chinesePhonePlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入您的中国电话'**
  String get chinesePhonePlaceholder;

  /// No description provided for @wechat.
  ///
  /// In zh, this message translates to:
  /// **'微信号'**
  String get wechat;

  /// No description provided for @wechatPlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入您的微信号'**
  String get wechatPlaceholder;

  /// No description provided for @save.
  ///
  /// In zh, this message translates to:
  /// **'保存'**
  String get save;

  /// No description provided for @quickEntry.
  ///
  /// In zh, this message translates to:
  /// **'快速输入'**
  String get quickEntry;

  /// No description provided for @quickEntryMessage.
  ///
  /// In zh, this message translates to:
  /// **'根据您填写的内容，匹配到如下信息，您可以选择正确信息快速填入'**
  String get quickEntryMessage;

  /// No description provided for @quickEntryNotAvailable.
  ///
  /// In zh, this message translates to:
  /// **'请继续输入以下信息'**
  String get quickEntryNotAvailable;

  /// No description provided for @confirmSelect.
  ///
  /// In zh, this message translates to:
  /// **'确认选择'**
  String get confirmSelect;

  /// No description provided for @cancelSelect.
  ///
  /// In zh, this message translates to:
  /// **'都不是'**
  String get cancelSelect;

  /// No description provided for @imageFromAlbum.
  ///
  /// In zh, this message translates to:
  /// **'从相册选择'**
  String get imageFromAlbum;

  /// No description provided for @imageFromCamera.
  ///
  /// In zh, this message translates to:
  /// **'拍照'**
  String get imageFromCamera;

  /// No description provided for @allQuoteTruckTypes.
  ///
  /// In zh, this message translates to:
  /// **'全部车型'**
  String get allQuoteTruckTypes;

  /// No description provided for @allQuoteAreas.
  ///
  /// In zh, this message translates to:
  /// **'全部地区'**
  String get allQuoteAreas;

  /// No description provided for @latestQuote.
  ///
  /// In zh, this message translates to:
  /// **'最新报价'**
  String get latestQuote;

  /// No description provided for @quoteDelta.
  ///
  /// In zh, this message translates to:
  /// **'日涨跌比'**
  String get quoteDelta;

  /// No description provided for @quoteDetail.
  ///
  /// In zh, this message translates to:
  /// **'行情详情'**
  String get quoteDetail;

  /// No description provided for @quoteHistory.
  ///
  /// In zh, this message translates to:
  /// **'历史报价'**
  String get quoteHistory;

  /// No description provided for @quoteTime.
  ///
  /// In zh, this message translates to:
  /// **'报价时间'**
  String get quoteTime;

  /// No description provided for @quotePrice.
  ///
  /// In zh, this message translates to:
  /// **'价格'**
  String get quotePrice;

  /// No description provided for @waitForDriverVerificationTitle.
  ///
  /// In zh, this message translates to:
  /// **'提交审核'**
  String get waitForDriverVerificationTitle;

  /// No description provided for @waitForDriverVerificationMessage.
  ///
  /// In zh, this message translates to:
  /// **'您已成功提交审核，我们将会在3个工作日内审核完毕，期间请保持电话畅通。是否进行车辆认证？'**
  String get waitForDriverVerificationMessage;

  /// No description provided for @waitForTruckVerificationMessage.
  ///
  /// In zh, this message translates to:
  /// **'您已成功提交审核，我们将会在3个工作日内审核完毕，期间请保持电话畅通，审核通过后可立即接单。'**
  String get waitForTruckVerificationMessage;

  /// No description provided for @notYet.
  ///
  /// In zh, this message translates to:
  /// **'先不了'**
  String get notYet;

  /// No description provided for @goVerify.
  ///
  /// In zh, this message translates to:
  /// **'立即认证'**
  String get goVerify;

  /// No description provided for @understand.
  ///
  /// In zh, this message translates to:
  /// **'知道了'**
  String get understand;

  /// No description provided for @noTruck.
  ///
  /// In zh, this message translates to:
  /// **'暂无车辆，去'**
  String get noTruck;

  /// No description provided for @add.
  ///
  /// In zh, this message translates to:
  /// **'新增'**
  String get add;

  /// No description provided for @addTruckTitle.
  ///
  /// In zh, this message translates to:
  /// **'新增车辆'**
  String get addTruckTitle;

  /// No description provided for @license.
  ///
  /// In zh, this message translates to:
  /// **'车牌号'**
  String get license;

  /// No description provided for @licensePlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入车牌号'**
  String get licensePlaceholder;

  /// No description provided for @licenseImage.
  ///
  /// In zh, this message translates to:
  /// **'车辆行驶证'**
  String get licenseImage;

  /// No description provided for @next.
  ///
  /// In zh, this message translates to:
  /// **'下一步'**
  String get next;

  /// No description provided for @truckType.
  ///
  /// In zh, this message translates to:
  /// **'车辆类型'**
  String get truckType;

  /// No description provided for @tonnage.
  ///
  /// In zh, this message translates to:
  /// **'车载吨位'**
  String get tonnage;

  /// No description provided for @tonnagePlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请选择吨位'**
  String get tonnagePlaceholder;

  /// No description provided for @volume.
  ///
  /// In zh, this message translates to:
  /// **'车载体积'**
  String get volume;

  /// No description provided for @volumePlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请选择体积'**
  String get volumePlaceholder;

  /// No description provided for @confirm.
  ///
  /// In zh, this message translates to:
  /// **'确定'**
  String get confirm;

  /// No description provided for @verified.
  ///
  /// In zh, this message translates to:
  /// **'已认证'**
  String get verified;

  /// No description provided for @verificationEmpty.
  ///
  /// In zh, this message translates to:
  /// **'待认证'**
  String get verificationEmpty;

  /// No description provided for @verificationPending.
  ///
  /// In zh, this message translates to:
  /// **'认证中'**
  String get verificationPending;

  /// No description provided for @verificationRejected.
  ///
  /// In zh, this message translates to:
  /// **'认证失败'**
  String get verificationRejected;

  /// No description provided for @verificationRejectedReason.
  ///
  /// In zh, this message translates to:
  /// **'请联系客服'**
  String get verificationRejectedReason;

  /// No description provided for @deleteTruckMessage.
  ///
  /// In zh, this message translates to:
  /// **'您正在删除关联车辆，确认删除吗？'**
  String get deleteTruckMessage;

  /// No description provided for @confirmDelete.
  ///
  /// In zh, this message translates to:
  /// **'确认删除'**
  String get confirmDelete;

  /// No description provided for @cancelDelete.
  ///
  /// In zh, this message translates to:
  /// **'不删除'**
  String get cancelDelete;

  /// No description provided for @noBank.
  ///
  /// In zh, this message translates to:
  /// **'暂无银行信息，去'**
  String get noBank;

  /// No description provided for @addBank.
  ///
  /// In zh, this message translates to:
  /// **'新增'**
  String get addBank;

  /// No description provided for @addBankTitle.
  ///
  /// In zh, this message translates to:
  /// **'新增银行信息'**
  String get addBankTitle;

  /// No description provided for @selectBankType.
  ///
  /// In zh, this message translates to:
  /// **'选择账户类型'**
  String get selectBankType;

  /// No description provided for @individual.
  ///
  /// In zh, this message translates to:
  /// **'个人账户'**
  String get individual;

  /// No description provided for @business.
  ///
  /// In zh, this message translates to:
  /// **'个体工商户'**
  String get business;

  /// No description provided for @individualName.
  ///
  /// In zh, this message translates to:
  /// **'姓名'**
  String get individualName;

  /// No description provided for @individualNamePlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入个人姓名'**
  String get individualNamePlaceholder;

  /// No description provided for @bankName.
  ///
  /// In zh, this message translates to:
  /// **'开户行名称'**
  String get bankName;

  /// No description provided for @bankNamePlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入开户行名称'**
  String get bankNamePlaceholder;

  /// No description provided for @bankAccount.
  ///
  /// In zh, this message translates to:
  /// **'账号'**
  String get bankAccount;

  /// No description provided for @bankAccountPlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入账号'**
  String get bankAccountPlaceholder;

  /// No description provided for @submit.
  ///
  /// In zh, this message translates to:
  /// **'提交'**
  String get submit;

  /// No description provided for @swiftAccount.
  ///
  /// In zh, this message translates to:
  /// **'KZT SWIFT 代码 BIC'**
  String get swiftAccount;

  /// No description provided for @swiftAccountPlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入KZT SWIFT 代码 BIC'**
  String get swiftAccountPlaceholder;

  /// No description provided for @businessOwnerName.
  ///
  /// In zh, this message translates to:
  /// **'姓名'**
  String get businessOwnerName;

  /// No description provided for @businessOwnerNamePlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入个体工商户姓名'**
  String get businessOwnerNamePlaceholder;

  /// No description provided for @taxNumber.
  ///
  /// In zh, this message translates to:
  /// **'税号'**
  String get taxNumber;

  /// No description provided for @taxNumberPlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入税号'**
  String get taxNumberPlaceholder;

  /// No description provided for @businessAddress.
  ///
  /// In zh, this message translates to:
  /// **'注册地址'**
  String get businessAddress;

  /// No description provided for @businessAddressPlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入个体工商户注册地址'**
  String get businessAddressPlaceholder;

  /// No description provided for @beneficiaryCode.
  ///
  /// In zh, this message translates to:
  /// **'受益人代码'**
  String get beneficiaryCode;

  /// No description provided for @beneficiaryCodePlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入受益人代码'**
  String get beneficiaryCodePlaceholder;

  /// No description provided for @usageCode.
  ///
  /// In zh, this message translates to:
  /// **'付款用途代码'**
  String get usageCode;

  /// No description provided for @usageCodePlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入付款用途代码'**
  String get usageCodePlaceholder;

  /// No description provided for @deleteBankMessage.
  ///
  /// In zh, this message translates to:
  /// **'您正在删除银行信息，确认删除吗？'**
  String get deleteBankMessage;

  /// No description provided for @submitSuccess.
  ///
  /// In zh, this message translates to:
  /// **'提交成功'**
  String get submitSuccess;

  /// No description provided for @switchTruck.
  ///
  /// In zh, this message translates to:
  /// **'切换'**
  String get switchTruck;

  /// No description provided for @loadingImage.
  ///
  /// In zh, this message translates to:
  /// **'正在下载图片'**
  String get loadingImage;

  /// No description provided for @selectTruck.
  ///
  /// In zh, this message translates to:
  /// **'选择车辆'**
  String get selectTruck;

  /// No description provided for @uid.
  ///
  /// In zh, this message translates to:
  /// **'用户编号'**
  String get uid;

  /// No description provided for @copyToClipboard.
  ///
  /// In zh, this message translates to:
  /// **'已复制到剪贴板'**
  String get copyToClipboard;

  /// No description provided for @preventTruckDeleteTitle.
  ///
  /// In zh, this message translates to:
  /// **'车辆信息认证中'**
  String get preventTruckDeleteTitle;

  /// No description provided for @preventTruckDelete.
  ///
  /// In zh, this message translates to:
  /// **'车辆信息正在认证，请稍后再操作，或者联系客服加速处理。'**
  String get preventTruckDelete;

  /// No description provided for @contactForVerification.
  ///
  /// In zh, this message translates to:
  /// **'联系客服加速认证'**
  String get contactForVerification;

  /// No description provided for @notVerifiedDriverTitle.
  ///
  /// In zh, this message translates to:
  /// **'尚未认证'**
  String get notVerifiedDriverTitle;

  /// No description provided for @notVerifiedDriverMessage.
  ///
  /// In zh, this message translates to:
  /// **'您的资料不完善，目前不能接单，请及时去完善您的资料，立即开启接单'**
  String get notVerifiedDriverMessage;

  /// No description provided for @notVerifiedTruckTitle.
  ///
  /// In zh, this message translates to:
  /// **'暂无车辆'**
  String get notVerifiedTruckTitle;

  /// No description provided for @notVerifiedTruckMessage.
  ///
  /// In zh, this message translates to:
  /// **'您尚未维护车辆信息，目前不能接单，请及时去完善车辆信息，立即开启接单'**
  String get notVerifiedTruckMessage;

  /// No description provided for @verifyingDriverTitle.
  ///
  /// In zh, this message translates to:
  /// **'正在认证'**
  String get verifyingDriverTitle;

  /// No description provided for @verifyingDriverMessage.
  ///
  /// In zh, this message translates to:
  /// **'您的资料正在认证中，目前不能接单，您可联系客服加速认证'**
  String get verifyingDriverMessage;

  /// No description provided for @verifyingTruckTitle.
  ///
  /// In zh, this message translates to:
  /// **'车辆认证中'**
  String get verifyingTruckTitle;

  /// No description provided for @verifyingTruckMessage.
  ///
  /// In zh, this message translates to:
  /// **'您维护车辆信息正在认证中，目前不能接单，您可联系客服加速认证'**
  String get verifyingTruckMessage;

  /// No description provided for @orderDetail.
  ///
  /// In zh, this message translates to:
  /// **'订单详情'**
  String get orderDetail;

  /// No description provided for @orderDistance.
  ///
  /// In zh, this message translates to:
  /// **'订单全程距离'**
  String get orderDistance;

  /// No description provided for @orderLoadingAddress.
  ///
  /// In zh, this message translates to:
  /// **'装货地点'**
  String get orderLoadingAddress;

  /// No description provided for @orderUnloadingAddress.
  ///
  /// In zh, this message translates to:
  /// **'卸货地点'**
  String get orderUnloadingAddress;

  /// No description provided for @orderLoadingTime.
  ///
  /// In zh, this message translates to:
  /// **'装货日期'**
  String get orderLoadingTime;

  /// No description provided for @orderCost.
  ///
  /// In zh, this message translates to:
  /// **'运费'**
  String get orderCost;

  /// No description provided for @makeQuote.
  ///
  /// In zh, this message translates to:
  /// **'报价协商'**
  String get makeQuote;

  /// No description provided for @placeOrder.
  ///
  /// In zh, this message translates to:
  /// **'立即接单'**
  String get placeOrder;

  /// No description provided for @orderManagement.
  ///
  /// In zh, this message translates to:
  /// **'订单管理'**
  String get orderManagement;

  /// No description provided for @orderStatusMatching.
  ///
  /// In zh, this message translates to:
  /// **'匹配中'**
  String get orderStatusMatching;

  /// No description provided for @orderStatusOngoing.
  ///
  /// In zh, this message translates to:
  /// **'进行中'**
  String get orderStatusOngoing;

  /// No description provided for @orderStatusFinished.
  ///
  /// In zh, this message translates to:
  /// **'已完成'**
  String get orderStatusFinished;

  /// No description provided for @orderStatusCanceled.
  ///
  /// In zh, this message translates to:
  /// **'已取消'**
  String get orderStatusCanceled;

  /// No description provided for @allCities.
  ///
  /// In zh, this message translates to:
  /// **'全部地区'**
  String get allCities;

  /// No description provided for @allPrices.
  ///
  /// In zh, this message translates to:
  /// **'全部价格'**
  String get allPrices;

  /// No description provided for @priceAbove.
  ///
  /// In zh, this message translates to:
  /// **'以上'**
  String get priceAbove;

  /// No description provided for @customerServiceWithWhatsapp.
  ///
  /// In zh, this message translates to:
  /// **'即将通过 WhatsApp 联系客服'**
  String get customerServiceWithWhatsapp;

  /// No description provided for @customerServiceWithPhone.
  ///
  /// In zh, this message translates to:
  /// **'即将通过电话联系客服'**
  String get customerServiceWithPhone;

  /// No description provided for @contact.
  ///
  /// In zh, this message translates to:
  /// **'联系'**
  String get contact;

  /// No description provided for @orderTargetPrice.
  ///
  /// In zh, this message translates to:
  /// **'货主目前报价'**
  String get orderTargetPrice;

  /// No description provided for @confirmQuote.
  ///
  /// In zh, this message translates to:
  /// **'确认出价'**
  String get confirmQuote;

  /// No description provided for @cancelQuote.
  ///
  /// In zh, this message translates to:
  /// **'取消出价'**
  String get cancelQuote;

  /// No description provided for @selectYourTruck.
  ///
  /// In zh, this message translates to:
  /// **'请选择您的车辆'**
  String get selectYourTruck;

  /// No description provided for @enterYourQuote.
  ///
  /// In zh, this message translates to:
  /// **'请输入您的报价'**
  String get enterYourQuote;

  /// No description provided for @operationSuccess.
  ///
  /// In zh, this message translates to:
  /// **'操作成功'**
  String get operationSuccess;

  /// No description provided for @operationFailed.
  ///
  /// In zh, this message translates to:
  /// **'操作失败，请稍后重试'**
  String get operationFailed;

  /// No description provided for @quoteRequired.
  ///
  /// In zh, this message translates to:
  /// **'请输入您的报价'**
  String get quoteRequired;

  /// No description provided for @confirmPlaceOrder.
  ///
  /// In zh, this message translates to:
  /// **'确认接单'**
  String get confirmPlaceOrder;

  /// No description provided for @cancelPlaceOrder.
  ///
  /// In zh, this message translates to:
  /// **'取消接单'**
  String get cancelPlaceOrder;

  /// No description provided for @placingOrder.
  ///
  /// In zh, this message translates to:
  /// **'正在处理订单，请稍后'**
  String get placingOrder;

  /// No description provided for @notifications.
  ///
  /// In zh, this message translates to:
  /// **'消息通知'**
  String get notifications;

  /// No description provided for @statusFindingSubtitle.
  ///
  /// In zh, this message translates to:
  /// **'货主发布新的订单'**
  String get statusFindingSubtitle;

  /// No description provided for @statusMatchingSubtitle.
  ///
  /// In zh, this message translates to:
  /// **'系统正在筛选匹配中'**
  String get statusMatchingSubtitle;

  /// No description provided for @statusPendingConfirmSubtitle.
  ///
  /// In zh, this message translates to:
  /// **'已经意向接单，等待货主确认'**
  String get statusPendingConfirmSubtitle;

  /// No description provided for @statusPendingContractSubtitle.
  ///
  /// In zh, this message translates to:
  /// **'合同正在签署中，随时注意电话'**
  String get statusPendingContractSubtitle;

  /// No description provided for @statusPendingLoadSubtitle.
  ///
  /// In zh, this message translates to:
  /// **'接单成功，尽快前往装货地装货吧'**
  String get statusPendingLoadSubtitle;

  /// No description provided for @statusQueueingSubtitle.
  ///
  /// In zh, this message translates to:
  /// **'排队中，耐心等待'**
  String get statusQueueingSubtitle;

  /// No description provided for @statusTransportSubtitle.
  ///
  /// In zh, this message translates to:
  /// **'开始运输了，记得每天报备位置哦'**
  String get statusTransportSubtitle;

  /// No description provided for @statusInClearanceSubtitle.
  ///
  /// In zh, this message translates to:
  /// **'排队清关中，耐心等待'**
  String get statusInClearanceSubtitle;

  /// No description provided for @statusHeadingDestinationSubtitle.
  ///
  /// In zh, this message translates to:
  /// **'正在前往目的地，运输中有问题请事件报备'**
  String get statusHeadingDestinationSubtitle;

  /// No description provided for @statusPendingUnloadSubtitle.
  ///
  /// In zh, this message translates to:
  /// **'已经到达目的地，请及时卸货哦'**
  String get statusPendingUnloadSubtitle;

  /// No description provided for @statusCompleteSubtitle.
  ///
  /// In zh, this message translates to:
  /// **'订单已完成，感谢您一路护送'**
  String get statusCompleteSubtitle;

  /// No description provided for @statusCancelledSubtitle.
  ///
  /// In zh, this message translates to:
  /// **'订单已取消，如有问题请联系沟通哦'**
  String get statusCancelledSubtitle;

  /// No description provided for @contactService.
  ///
  /// In zh, this message translates to:
  /// **'沟通联系'**
  String get contactService;

  /// No description provided for @orderInvalidStatus.
  ///
  /// In zh, this message translates to:
  /// **'已失效'**
  String get orderInvalidStatus;

  /// No description provided for @logs.
  ///
  /// In zh, this message translates to:
  /// **'日志'**
  String get logs;

  /// No description provided for @weightPhoto.
  ///
  /// In zh, this message translates to:
  /// **'拍照过磅'**
  String get weightPhoto;

  /// No description provided for @weightPhotoPlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请上传车辆过磅照片'**
  String get weightPhotoPlaceholder;

  /// No description provided for @finishLoad.
  ///
  /// In zh, this message translates to:
  /// **'装货完成'**
  String get finishLoad;

  /// No description provided for @activityReport.
  ///
  /// In zh, this message translates to:
  /// **'事件报备'**
  String get activityReport;

  /// No description provided for @locationNotAvailableTitle.
  ///
  /// In zh, this message translates to:
  /// **'定位失败'**
  String get locationNotAvailableTitle;

  /// No description provided for @locationNotAvailableMessage.
  ///
  /// In zh, this message translates to:
  /// **'需要获取您的定位处理运输订单，请检查您的定位权限是否开启。'**
  String get locationNotAvailableMessage;

  /// No description provided for @openSettings.
  ///
  /// In zh, this message translates to:
  /// **'打开设置'**
  String get openSettings;

  /// No description provided for @acquiringLocation.
  ///
  /// In zh, this message translates to:
  /// **'正在获取定位'**
  String get acquiringLocation;

  /// No description provided for @viewPayment.
  ///
  /// In zh, this message translates to:
  /// **'查看凭证'**
  String get viewPayment;

  /// No description provided for @temporaryStay.
  ///
  /// In zh, this message translates to:
  /// **'临时休息'**
  String get temporaryStay;

  /// No description provided for @temporaryStayPlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入休息原因'**
  String get temporaryStayPlaceholder;

  /// No description provided for @unexpectedDelay.
  ///
  /// In zh, this message translates to:
  /// **'特殊延误'**
  String get unexpectedDelay;

  /// No description provided for @unexpectedDelayPlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请输入延误原因'**
  String get unexpectedDelayPlaceholder;

  /// No description provided for @selectReportItem.
  ///
  /// In zh, this message translates to:
  /// **'请选择报备事项'**
  String get selectReportItem;

  /// No description provided for @acquireLocationFailed.
  ///
  /// In zh, this message translates to:
  /// **'定位失败，请稍后再试'**
  String get acquireLocationFailed;

  /// No description provided for @dailyReport.
  ///
  /// In zh, this message translates to:
  /// **'每日报备'**
  String get dailyReport;

  /// No description provided for @startClearance.
  ///
  /// In zh, this message translates to:
  /// **'开始清关'**
  String get startClearance;

  /// No description provided for @endClearance.
  ///
  /// In zh, this message translates to:
  /// **'清关完毕'**
  String get endClearance;

  /// No description provided for @arriveDestination.
  ///
  /// In zh, this message translates to:
  /// **'到达目的地'**
  String get arriveDestination;

  /// No description provided for @unloadComplete.
  ///
  /// In zh, this message translates to:
  /// **'卸货完毕'**
  String get unloadComplete;

  /// No description provided for @uploadReceipt.
  ///
  /// In zh, this message translates to:
  /// **'上传交接单'**
  String get uploadReceipt;

  /// No description provided for @uploadReceiptPlaceholder.
  ///
  /// In zh, this message translates to:
  /// **'请上传交接单照片'**
  String get uploadReceiptPlaceholder;

  /// No description provided for @cannotTakeOrderTitle.
  ///
  /// In zh, this message translates to:
  /// **'无法接单'**
  String get cannotTakeOrderTitle;

  /// No description provided for @cannotTakeOrderMessage.
  ///
  /// In zh, this message translates to:
  /// **'您目前有正在进行中的订单，请完成当前订单后再接新订单。'**
  String get cannotTakeOrderMessage;

  /// No description provided for @signOut.
  ///
  /// In zh, this message translates to:
  /// **'退出登录'**
  String get signOut;

  /// No description provided for @signOutMessage.
  ///
  /// In zh, this message translates to:
  /// **'您正在退出账户，请确认是否退出。'**
  String get signOutMessage;

  /// No description provided for @deleteAccount.
  ///
  /// In zh, this message translates to:
  /// **'注销账号'**
  String get deleteAccount;

  /// No description provided for @deleteAccountMessage.
  ///
  /// In zh, this message translates to:
  /// **'您正在注销账户，注销后数据将清除，请确认。'**
  String get deleteAccountMessage;

  /// No description provided for @accountDeletedMessage.
  ///
  /// In zh, this message translates to:
  /// **'账号已注销，请联系客服。'**
  String get accountDeletedMessage;

  /// No description provided for @unauthenticatedError.
  ///
  /// In zh, this message translates to:
  /// **'App认证失败，请从官方渠道下载App'**
  String get unauthenticatedError;

  /// No description provided for @onGoingOrder.
  ///
  /// In zh, this message translates to:
  /// **'进行中订单'**
  String get onGoingOrder;

  /// No description provided for @orderWeightPhotoMessage.
  ///
  /// In zh, this message translates to:
  /// **'请您尽快前往装货地装货，记得一定要过磅拍照哈。'**
  String get orderWeightPhotoMessage;

  /// No description provided for @orderFinishLoadMessage.
  ///
  /// In zh, this message translates to:
  /// **'您是否已经装车完毕，如果已经装货完毕，请点击装货完毕。'**
  String get orderFinishLoadMessage;

  /// No description provided for @orderStartClearanceMessage.
  ///
  /// In zh, this message translates to:
  /// **'您可能已经到了海关清关，请确认是否已经开始清关。'**
  String get orderStartClearanceMessage;

  /// No description provided for @orderEndClearanceMessage.
  ///
  /// In zh, this message translates to:
  /// **'您可能已经完成海关清关，请确认是否已完成清关。'**
  String get orderEndClearanceMessage;

  /// No description provided for @orderArriveDestinationMessage.
  ///
  /// In zh, this message translates to:
  /// **'您可能已经到达卸货地，请确认是否已到达目的地。'**
  String get orderArriveDestinationMessage;

  /// No description provided for @orderUploadReceiptMessage.
  ///
  /// In zh, this message translates to:
  /// **'您可能已经卸货完成，请上传交接单。'**
  String get orderUploadReceiptMessage;

  /// No description provided for @orderPositionReportMessage.
  ///
  /// In zh, this message translates to:
  /// **'您有进行中订单，请点击报备位置。'**
  String get orderPositionReportMessage;

  /// No description provided for @reportPosition.
  ///
  /// In zh, this message translates to:
  /// **'报备位置'**
  String get reportPosition;

  /// No description provided for @guestAccess.
  ///
  /// In zh, this message translates to:
  /// **'Guest Access'**
  String get guestAccess;

  /// No description provided for @reachBottom.
  ///
  /// In zh, this message translates to:
  /// **'- 已经到最底了 -'**
  String get reachBottom;

  /// No description provided for @notVerifiedCannotViewQuotes.
  ///
  /// In zh, this message translates to:
  /// **'您的资料不完善，目前不能查看行情详情，请及时去完善您的资料。'**
  String get notVerifiedCannotViewQuotes;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'kk', 'ru', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'kk': return AppLocalizationsKk();
    case 'ru': return AppLocalizationsRu();
    case 'zh': return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
