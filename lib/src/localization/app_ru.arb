{"appTitle": "GoFreight", "welcome": "Добро пожаловать в", "use": "Добро пожаловать", "googleLogin": "Вход с помощью Google", "appleLogin": "Авторизация Apple", "agreementText": "Я прочитал и согласен с ", "userAgreement": "《Соглашением об использовании программного обеспечения GoFreight》、", "privacyAgreement": "《Соглашением о конфиденциальности GoFreight》、", "infoAgreement": "《Соглашением о предоставлении информации GoFreight》", "agreementText1": ".", "contactUs": "Если возникли проблемы, свяжитесь с нами", "notAgreeMessage1": "Об<PERSON>ружен<PERSON>, что вы еще не прочитали соглашение. Пожалуйста, внимательно прочитайте и примите", "notAgreeMessage2": ", Нажмите «Согласен», чтобы начать пользоваться нашими продуктами и услугами.", "cancel": "Отменить", "agree": "Согласен", "readAgreement": "Прочитайте соглашение", "home": "Главная", "order": "Зак<PERSON>з", "mine": "<PERSON><PERSON><PERSON>", "networkError": "Сетевая аномалия，Пожалуйста попробуйте позже", "loadFrom": "Из {origin}", "@loadFrom": {"placeholders": {"origin": {}}}, "selectDestinationCountry": "Выберите страну, в которую вы собираетесь отправиться", "loadingTruck": "Транспортное средство: ", "loadingTruckNotRegistered": "Вы не сохранили информацию о транспортном средстве，Пойти", "registerTruck": "Усовершенствование информации о транспортном средстве", "loadingTruckNotVerified": "Информация об автомобиле не сертифицирована，Пожалуйста ", "quoteCenter": "Центр цен", "quoteFrom": "Отправление из {origin}", "@quoteFrom": {"placeholders": {"origin": {}}}, "rankByPrice": "Сортировка по цене", "rankByPref": "Сортировка по предпочтениям", "pickupDate": "Время получения:", "distance": "Общая протяженность {distance}km", "@distance": {"placeholders": {"distance": {}}}, "loading": "Погрузка", "unloading": "Разгрузка", "noOrder": "Нет заказов", "noData": "Нет данных", "viewOrder": "Просмотреть заказ", "rankByPrefNotice": "Вам необходимо сейчас вверху указать место погрузки и разгрузки, а также транспортное средство，только после этого вы сможете установить предпочтения", "login": "Вход", "verifyTitle": "Сертифицированный водитель платформы, быстрое принятие заказа", "verifyAction": "Перейти к сертификации", "massiveOrders": "Большое количество заказов", "fastPay": "Своевременные расчеты", "safe": "Гарантии платформы", "driverCenter": "Центр водителей", "driverVerify": "Сертификация водителей", "truckManagement": "Управление транспортными средствами", "bankInfo": "Банковская информация", "serviceCenter": "Сервисный центр", "customerService": "Обратитесь в службу поддержки клиентов", "guarantee": "Соглашение о обязательстве предоставления информации", "userAgreementAction": "Пользовательское соглашение", "privacyAgreementAction": "Политика конфиденциальности", "switchLanguage": "Смена языка", "completeInfo": "Пожалуйста, дополните информацию", "userInfo": "Личная информация", "phone": "Телефон ", "phonePlaceholder": "Пожалуйста введите номер телефона", "driverName": "Имя водителя（На английском языке）", "driverNamePlaceholder": "Введите свое имя", "passportPhoto": "Фотография", "moreContactInfo": "Больше контактов", "chinesePhone": "Телефон в Китае", "chinesePhonePlaceholder": "Введите свой телефон в Китае", "wechat": "WeChat", "wechatPlaceholder": "Введите свой WeChat ID", "save": "Сохранить", "quickEntry": "Быстрый ввод", "quickEntryMessage": "На основании данных, которые вы заполнили，были найдены следующие совпадающие сведения，Вы можете выбрать правильную информацию для быстрого заполнения", "quickEntryNotAvailable": "Продолжайте вводить следующую информацию", "confirmSelect": "Подтвердить выбор", "cancelSelect": "Ни одно из вышеперечисленных", "imageFromAlbum": "Выберите из альбомов", "imageFromCamera": "Фотографировать ", "allQuoteTruckTypes": "Все модели", "allQuoteAreas": "Все регионы", "latestQuote": "Последние предложения", "quoteDelta": "Ежедневный рост/падение", "quoteDetail": "Детальная информация", "quoteHistory": "Исторические котировки", "quoteTime": "Время котировки", "quotePrice": "Цена", "waitForDriverVerificationTitle": "Отправить на проверку", "waitForDriverVerificationMessage": "Вы успешно подали заявку на аудит， мы будем проверены в течение 3 рабочих дней，в течение этого периода пожалуйста держите телефон гладким。 ли транспортное средство сертификации？", "waitForTruckVerificationMessage": "Вы были успешно отправлены на аудит，мы будем проверены в течение 3 рабочих дней，в течение этого периода пожалуйста держите телефон гладким，проверено немедленное получение заказов。", "notYet": "Сейчас не могу", "goVerify": "Пройти проверку", "understand": "Понятно", "noTruck": "Нет транспортных средств, добавить ", "add": "новый", "addTruckTitle": "новый добавленный автомобиль", "license": "номерной знак", "licensePlaceholder": "Пожалуйста введите номер машины", "licenseImage": "Технический паспорт", "next": "Следующий", "truckType": "Тип автомобиля", "tonnage": "Грузоподъемность", "tonnagePlaceholder": "Пожалуйста выберите тоннаж", "volume": "Объем кузова", "volumePlaceholder": "Пожалуйста выберите объем", "confirm": "Подтверждено", "verified": "Сертифицированный", "verificationEmpty": "Ожидающие сертификации", "verificationPending": "Выполняется аутентификация", "verificationRejected": "Аутентификация не прошла", "verificationRejectedReason": "Обратитесь в службу поддержки клиентов", "deleteTruckMessage": "Вы удаляете связанные транспортные средства. Подтвердить удаление?", "confirmDelete": "Подтвердить удаление", "cancelDelete": "Не удалять", "noBank": "Информация о банке отсутствует, ", "addBank": "добавить", "addBankTitle": "Добавлена информация о банке", "selectBankType": "Выберите тип счета", "individual": "Физическое лицо", "business": "Индивидуальный предприниматель", "individualName": "Имя и фамилия", "individualNamePlaceholder": "Введите свое имя", "bankName": "Название банка, в котором открыт счет", "bankNamePlaceholder": "Введите название банка, в котором открыт счет", "bankAccount": "Номер счета", "bankAccountPlaceholder": "Введите номер своего счета", "submit": "представить", "swiftAccount": "KZT Код SWIFT", "swiftAccountPlaceholder": "Введите код SWIFT вашего банка", "businessOwnerName": "Имя", "businessOwnerNamePlaceholder": "Введите название вашего индивидуального предпринимателя", "taxNumber": "Налоговый номер", "taxNumberPlaceholder": "Введите ваш налоговый номер", "businessAddress": "Адрес регистрации", "businessAddressPlaceholder": "Введите адрес регистрации вашего индивидуального предпринимателя", "beneficiaryCode": "Код бенефициара", "beneficiaryCodePlaceholder": "Введите код бенефициара", "usageCode": "Код назначения платежа*", "usageCodePlaceholder": "Введите код цели платежа", "deleteBankMessage": "Вы удаляете свои банковские реквизиты，подтверждаете удаление？", "submitSuccess": "Отправлено", "switchTruck": " с переключением", "loadingImage": "Сейчас загружается изображение", "selectTruck": "Выбор транспортных средств", "uid": "Номер пользователя", "copyToClipboard": "Скопировано в буфер обмена", "preventTruckDeleteTitle": "Выполняется проверка подлинности информации о транспортном средстве", "preventTruckDelete": "Информация о транспортном средстве проходит проверку подлинности，пожалуйста подождите некоторое время или обратитесь в службу поддержки，чтобы ускорить процесс。", "contactForVerification": "Обратитесь в службу поддержки чтобы ускорить сертификацию", "notVerifiedDriverTitle": "Не подтверждено", "notVerifiedDriverMessage": "Ваши данные неполны, в настоящее время мы не можем принимать заказы. Пожалуйста дополните свои данные, чтобы сразу начать принимать заказы", "notVerifiedTruckTitle": "В настоящее время нет транспортных средств", "notVerifiedTruckMessage": "Вы еще не обновили информацию о транспортном средстве, поэтому в настоящее время не можете принимать заказы. Пожалуйста, дополните информацию о транспортном средстве и сразу же начните принимать заказы", "verifyingDriverTitle": "Выполняется аутентификация", "verifyingDriverMessage": "Ваша информация проходит сертификацию， в настоящее время мы не можем принимать заказы，вы можете связаться со службой поддержки чтобы ускорить сертификацию", "verifyingTruckTitle": "Проводится сертификация автомобилей", "verifyingTruckMessage": "Ваша информация проходит сертификацию，в настоящее время мы не можем принимать заказы，вы можете связаться со службой поддержки чтобы ускорить сертификацию", "orderDetail": "Детали заказа", "orderDistance": "Общая дистанция заказа", "orderLoadingAddress": "Место погрузки", "orderUnloadingAddress": "Место разгрузки", "orderLoadingTime": "Дата погрузки", "orderCost": "Плата за перевозку", "makeQuote": "Переговоров о цене", "placeOrder": "Принять заказ немедленно", "orderManagement": "Управление заказами", "orderStatusMatching": "Подбора", "orderStatusOngoing": "В процессе", "orderStatusFinished": "Завершено", "orderStatusCanceled": "Отменено", "allCities": "Все регионы", "allPrices": "Все цены", "priceAbove": " Более", "customerServiceWithWhatsapp": "Скоро свяжусь с службой поддержки через WhatsAp", "customerServiceWithPhone": "Скоро свяжусь с службой поддержки по телефону", "contact": "Связаться", "orderTargetPrice": "Текущие цены от грузоотправителей", "confirmQuote": "Потвердить цену", "cancelQuote": "Отменить цену", "selectYourTruck": "Пожалуйста выберите ваш автомобиль", "enterYourQuote": "Пожалуйста введите свою цену", "operationSuccess": "Операция прошла успешно", "operationFailed": "Операция не удалась，Пожалуйста повторите попытку позднее", "quoteRequired": "Пожалуйста введите свою цену", "confirmPlaceOrder": "Подтвердить прием заказа", "cancelPlaceOrder": "Отменить прием заказа", "placingOrder": "аказ обрабатывается, пожалуйста подождите", "notifications": "Уведомления", "statusFindingSubtitle": "Грузоотправитель опубликовал новый заказ", "statusMatchingSubtitle": "Система в процессе отбора и подбора", "statusPendingConfirmSubtitle": "Интенция принять заказ уже есть，ожидается подтверждение грузоотправителя", "statusPendingContractSubtitle": "Договор находится в процессе подписания，следите за звонками", "statusPendingLoadSubtitle": "Прием заказа успешен，пожалуйста как можно раньше направитесь на место погрузки для погрузки", "statusQueueingSubtitle": "В очереди，пожалуйста подождите с терпением", "statusTransportSubtitle": "Транспортировка началась，Не забудьте ежедневно сообщать о своей локации", "statusInClearanceSubtitle": "В очереди на таможенную очистку， пожалуйста подождите с терпением", "statusHeadingDestinationSubtitle": "Направляюсь к пункту назначения，При возникновении проблем во время транспортировки сообщайте вовремя", "statusPendingUnloadSubtitle": "Уже достигнут пункт назначения，пожалуйста своевременно выполните разгрузку", "statusCompleteSubtitle": "Заказ завершен， Спасибо за вашу сопровождение в пути", "statusCancelledSubtitle": "Заказ отменен，При возникновении вопросов пожалуйста свяжитесь для обсуждения", "contactService": "Связаться и обсудить", "orderInvalidStatus": "Устарело", "logs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weightPhoto": "Сфотографировать и взвесить", "weightPhotoPlaceholder": "Пожалуйста загрузите фотографию взвешивания транспортного средства", "finishLoad": "Погрузка завершена", "activityReport": "Сообщение о событии", "locationNotAvailableTitle": "Определение местоположения не удалось", "locationNotAvailableMessage": "Для обработки транспортного заказа необходимо получить ваше местоположение，Пожалуйста проверьте включены ли разрешения на определение местоположения。", "openSettings": "Открыть настройки", "acquiringLocation": "Получение местоположения в процессе", "viewPayment": "Просмотреть документы основания", "temporaryStay": "Временный отдых", "temporaryStayPlaceholder": "Пожалуйста введите причину отдыха", "unexpectedDelay": "Особые задержки", "unexpectedDelayPlaceholder": "Пожалуйста введите причину задержки", "selectReportItem": "Выберите тип события", "acquireLocationFailed": "Определение местоположения не удалось，Пожалуйста попробуйте позже", "dailyReport": "Ежедневная регистрация", "startClearance": "Начать таможенную очистку", "endClearance": "Таможенная очистка завершена", "arriveDestination": "Прибыло на место назначения", "unloadComplete": "Разгрузка завершена", "uploadReceipt": "Загрузить акт приемки передачи", "uploadReceiptPlaceholder": "Пожалуйста загрузите фотографию акта приемки передачи", "cannotTakeOrderTitle": "Нельзя принять заказ", "cannotTakeOrderMessage": "У вас в настоящее время есть активный заказ，Пожалуйста завершите текущий заказ прежде чем принимать новый。", "signOut": "Выйти из системы", "signOutMessage": "Вы выходите из учетной записи，Пожалуйста подтвердите хотите ли вы выйти。", "deleteAccount": "Удалить учетную запись", "deleteAccountMessage": "Вы удаляете свою учетную запись，После удаления данные будут очищены，Пожалуйста подтвердите。", "accountDeletedMessage": "Учетная запись была удалена，Пожалуйста свяжитесь с поддержкой。", "unauthenticatedError": "Аутентификация приложения не удалась，Пожалуйста скачайте приложение через официальные каналы", "onGoingOrder": "Активный заказ", "orderWeightPhotoMessage": "Пожалуйста как можно скорее направьтесь на место погрузки для погрузки，Не забудьте обязательно взвесить и сфотографировать。", "orderFinishLoadMessage": "Вы уже завершили погрузку，Если да пожалуйста нажмите，Погрузка завершена。", "orderStartClearanceMessage": "Вероятно вы прибыли к таможне для таможенной очистки，Пожалуйста подтвердите началась ли таможенная очистка。", "orderEndClearanceMessage": "Вероятно вы завершили таможенную очистку，Пожалуйста подтвердите  завершена таможенная очистка。", "orderArriveDestinationMessage": "Вероятно вы уже прибыли до места разгрузки，Пожалуйста подтвердите прибыли до места назначения。", "orderUploadReceiptMessage": "Вероятно вы уже завершили разгрузку，Пожалуйста загрузите акт приемки передачи。", "orderPositionReportMessage": "У вас есть активный заказ，Пожалуйста нажмите чтобы сообщить о местоположении。", "reportPosition": "Сообщить о местоположении", "guestAccess": "Guest Access", "reachBottom": "- Уже дошли до конца -", "notVerifiedCannotViewQuotes": "Ваши данные неполные， В настоящее время вы не можете просматривать детали рыночной ситуации，Пожалуйста своевременно дополните ваши данные。"}