import 'dart:async';

import 'package:driver/src/mixins/order.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/firestore.dart';
import 'package:event_bus/event_bus.dart';
import 'package:flutter/material.dart';

class OrderView extends StatefulWidget {
  const OrderView({super.key});

  static EventBus eventBus = EventBus();

  @override
  State<StatefulWidget> createState() => _OrderViewState();
}

class _OrderViewState extends State<OrderView>
    with PageMixin, TickerProviderStateMixin, OrderMixin {
  late TabController _tabController;
  List<Order> _orders = [];
  int _tabIndex = 0;
  late StreamSubscription _updateSubscription;

  @override
  void initState() {
    _tabController = TabController(length: 4, vsync: this);
    _updateSubscription = OrderView.eventBus
        .on<OrderUpdateEvent>()
        .listen((_) => updateOrders(emptyOrders: false));
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      updateOrders();
    });
  }

  @override
  void dispose() {
    _updateSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return pageBase(appLoc(context).orderManagement,
        _orders.isEmpty ? emptyIcon(type: EmptyIconType.order) : orderList(),
        paddingTop: 48,
        fixedContent: Positioned(
            top: 0,
            width: MediaQuery.of(context).size.width,
            child: Container(
                color: backgroundColor,
                child: TabBar(
                    controller: _tabController,
                    indicatorColor: primaryColor,
                    dividerColor: Colors.transparent,
                    unselectedLabelColor: Colors.black54,
                    labelColor: Colors.black,
                    labelStyle:
                        TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                    onTap: (e) {
                      setState(() {
                        _tabIndex = e;
                        updateOrders();
                      });
                    },
                    tabs: [
                      Tab(text: appLoc(context).orderStatusMatching),
                      Tab(text: appLoc(context).orderStatusOngoing),
                      Tab(text: appLoc(context).orderStatusFinished),
                      Tab(text: appLoc(context).orderStatusCanceled)
                    ]))), onRefresh: () async {
      await updateOrders(emptyOrders: false);
    });
  }

  Widget orderList() {
    return Column(
      spacing: 10,
      children: _orders
          .map((order) => orderCard(User.shared, order,
              showViewOrderButton: false,
              showStatus: true,
              showPrice: _tabIndex == 1 || _tabIndex == 2))
          .toList(),
    );
  }

  @override
  Future updateOrders({bool emptyOrders = true}) async {
    if (emptyOrders) {
      setState(() {
        _orders = [];
      });
    }
    final currentId = _tabIndex;
    List<Order> orders = [];
    switch (_tabIndex) {
      case 0:
        orders = await Firestore.shared.getMatchingOrders();
        break;
      case 1:
        orders = await getOnGoingOrders();
        break;
      case 2:
        orders = await Firestore.shared.getOrders(false,
            statusList: [OrderStatus.completed],
            driverId: User.shared.driverVerification?.driverId);
        break;
      case 3:
        orders = await Firestore.shared.getCancelledAndInvalidatedOrders();
        break;
    }
    if (currentId == _tabIndex) {
      setState(() {
        _orders = orders;
      });
    } else {
      debugPrint('OrderView updateOrders: currentId != _tabIndex');
    }
  }

  @override
  bool canViewOrderDetail(Order order) {
    return true;
  }

  @override
  Color? statusColor() {
    switch (_tabIndex) {
      case 0:
        return primaryColor;
      case 1:
        return primaryColor;
      case 2:
        return Color(0xFF5ACF47);
      case 3:
        return Color(0xFF999999);
    }
    return null;
  }
}

class OrderUpdateEvent {}
