import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/selectable.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/file.dart';
import 'package:driver/src/utils/functions.dart';
import 'package:driver/src/widgets/dialog.dart';
import 'package:firebase_cached_image/firebase_cached_image.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

mixin EntryMixin<T extends StatefulWidget> on State<T> {
  bool _findingInfo = false;

  Widget formItem(
      {required String title, bool isRequired = false, required Widget input}) {
    return Container(
        width: MediaQuery.of(context).size.width - 24,
        padding: EdgeInsets.only(left: 10, right: 10, top: 10),
        margin: EdgeInsets.only(bottom: 10),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(4)),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(
            children: [
              Text(title, style: TextStyle(color: Color(0xFF666666))),
              if (isRequired)
                Text(' *', style: TextStyle(color: Color(0xFFF2242B)))
            ],
          ),
          input
        ]));
  }

  Future showImagePreview(String storagePath) async {
    showAppDialog(
        context,
        SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Stack(children: [
              Image(
                image: FirebaseImageProvider(FirebaseUrl.fromReference(
                    FirebaseStorage.instance.ref(storagePath))),
                fit: BoxFit.fitWidth,
                width: MediaQuery.of(context).size.width,
              ),
              Positioned(
                  top: 0,
                  right: 0,
                  child: IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(Icons.clear)))
            ])),
        [],
        titleTopPadding: 0);
  }

  Widget storageImage(String storagePath,
      {double width = 120, double height = 120}) {
    return InkWell(
        splashFactory: NoSplash.splashFactory,
        focusColor: Colors.transparent,
        onTap: () => showImagePreview(storagePath),
        child: Container(
          margin: EdgeInsets.symmetric(vertical: 10),
          width: width,
          height: height,
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(
              color: Color(0xFFF4F5F7), borderRadius: BorderRadius.circular(4)),
          child: Image(
            image: FirebaseImageProvider(FirebaseUrl.fromReference(
                FirebaseStorage.instance.ref(storagePath))),
            fit: BoxFit.cover,
          ),
        ));
  }

  bool _imageUploading = false;
  Widget imageUpload(
      String? storagePath, void Function(String?) onChange, String type,
      {double width = 120,
      double height = 120,
      Widget? placeholder,
      bool allowSelectFromGallery = true}) {
    return storagePath != null
        ? Stack(clipBehavior: Clip.none, children: [
            storageImage(storagePath, width: width, height: height),
            Positioned(
                top: 0,
                right: -10,
                child: IconButton(
                    onPressed: () {
                      onChange(null);
                    },
                    icon: Icon(Icons.clear)))
          ])
        : InkWell(
            onTap: () async {
              _imageUploading = true;
              String? path = await pickImageAndUploadToStorage(context, type,
                  allowSelectFromGallery: allowSelectFromGallery);
              _imageUploading = false;
              if (path != null) {
                onChange(path);
              }
            },
            child: Padding(
                padding: EdgeInsets.symmetric(vertical: 10),
                child: placeholder ??
                    Container(
                      width: width,
                      height: height,
                      decoration: BoxDecoration(
                          color: Color(0xFFF4F5F7),
                          borderRadius: BorderRadius.circular(4)),
                      child: Icon(Icons.camera_alt,
                          color: Color(0xFF999999), size: 32),
                    )));
  }

  Selectable? _dialogSelection;

  Future<U?> showSelectionDialog<U extends Selectable>(
      {required List<U> options,
      required String title,
      String? message,
      String? cancelText,
      int? initialSelectIndex}) async {
    if (options.isNotEmpty) {
      setState(() {
        _dialogSelection = options[initialSelectIndex ?? 0];
      });
      final ret = await showAppDialog(
          context,
          dialogMessage(
              messageWidget: StatefulBuilder(
                  builder: (c, setState) => Column(children: [
                        if (message != null)
                          Padding(
                              padding: EdgeInsets.only(bottom: 24),
                              child: Text(message, style: descStyle)),
                        ...options.map((e) => InkWell(
                            onTap: () {
                              setState(() {
                                _dialogSelection = e;
                              });
                            },
                            child: Padding(
                                padding: EdgeInsets.symmetric(vertical: 8),
                                child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      checkBox(_dialogSelection?.identifier() ==
                                          e.identifier()),
                                      Container(
                                          padding: EdgeInsets.only(left: 16),
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width -
                                              160,
                                          child: Text(e.label(),
                                              style: TextStyle(fontSize: 16)))
                                    ]))))
                      ]))),
          [
            dialogButton(context, cancelText ?? appLoc(context).cancelSelect,
                DialogButtonType.cancel),
            dialogButton(context, appLoc(context).confirmSelect,
                DialogButtonType.primary)
          ],
          title: title);
      if (ret == DialogButtonType.primary && _dialogSelection != null) {
        return _dialogSelection as U?;
      }
    }

    return null;
  }

  Future findInfo<U extends SelectableModel>(
      Future<List<U>> Function(String) find,
      String keyword,
      void Function(U) selected,
      {bool Function()? precondition}) async {
    FocusManager.instance.primaryFocus?.unfocus();
    if (precondition != null && !precondition()) return;
    if (keyword.isEmpty) return;
    if (_findingInfo) return;
    _findingInfo = true;
    Loader.show();
    final result = await find(keyword);
    debugPrint('find result: ${result.map((e) => e.displayName)}');
    Loader.hide();

    if (mounted) {
      if (result.isEmpty) {
        showSnackBar(context, text: appLoc(context).quickEntryNotAvailable);
      } else {
        final ret = await showSelectionDialog(
            options: result,
            title: appLoc(context).quickEntry,
            message: appLoc(context).quickEntryMessage);
        if (ret != null) {
          selected(ret);
        }
      }
    }

    _findingInfo = false;
  }

  Future<String?> getStorageImage(
      String funcName, SelectableModel selected) async {
    if (selected.storage() == null) {
      Loader.show(loadingText: appLoc(context).loadingImage);
      final path = await Functions.shared.getStorageImage(
          selected.id, selected.token(), selected.recordId, funcName);
      Loader.hide();
      return path;
    }
    return null;
  }

  Widget checkBox(bool isChecked) {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
          color: isChecked ? primaryColor : Colors.transparent,
          border:
              isChecked ? null : Border.all(color: Colors.black38, width: 1),
          borderRadius: BorderRadius.circular(24)),
      child:
          isChecked ? Icon(Icons.check, color: Colors.white, size: 16) : null,
    );
  }

  Widget textInput(TextEditingController controller, String hintText,
      {void Function()? onBlur,
      TextInputType? keyboardType,
      int? maxLength,
      bool useUpperCase = false,
      int maxLines = 1}) {
    return Focus(
      child: TextField(
        controller: controller,
        cursorColor: primaryColor,
        keyboardType: keyboardType,
        maxLength: maxLength,
        maxLines: maxLines,
        onChanged: (value) {
          setState(() {
            controller.text = useUpperCase ? value.toUpperCase() : value;
          });
        },
        decoration: InputDecoration(
            hintText: hintText,
            hintStyle: TextStyle(color: Color(0xFFB3B3B3)),
            border: InputBorder.none,
            counterText: ''),
      ),
      onFocusChange: (hasFocus) {
        if (!hasFocus) {
          onBlur?.call();
        }
      },
    );
  }

  Widget disablePopDuringBackgroundOps(Widget child) {
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (bool didPop, T? result) {
          if (didPop) return;
          if (!_findingInfo &&
              !_imageUploading &&
              Navigator.of(context).canPop()) {
            Navigator.of(context).pop(result);
          }
        },
        child: child);
  }
}
