import 'package:bot_toast/bot_toast.dart';
import 'package:driver/src/bank/info_entry.dart';
import 'package:driver/src/bank/view.dart';
import 'package:driver/src/driver/info.dart';
import 'package:driver/src/home/<USER>';
import 'package:driver/src/home/<USER>';
import 'package:driver/src/home/<USER>';
import 'package:driver/src/driver/info_entry.dart';
import 'package:driver/src/login/view.dart';
import 'package:driver/src/mine/notifications.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/model/notification.dart' as notif;
import 'package:driver/src/order/detail.dart';
import 'package:driver/src/order/logs.dart';
import 'package:driver/src/order/report.dart';
import 'package:driver/src/order/view.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:driver/src/truck/info_entry.dart';
import 'package:driver/src/truck/license_entry.dart';
import 'package:driver/src/truck/view.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:lsapp/lsapp.dart';
import 'package:driver/src/localization/app_localizations.dart'
    show AppLocalizations;

class MyApp extends StatelessWidget {
  const MyApp({
    super.key,
  });

  static bool lsappInitialized = false;
  static GlobalKey<NavigatorState> materialKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: SettingsController.shared,
      builder: (BuildContext context, Widget? child) {
        return MaterialApp(
          restorationScopeId: 'app',
          localizationsDelegates: AppLocalizations.localizationsDelegates,
          supportedLocales: AppLocalizations.supportedLocales,
          localeResolutionCallback: (locale, supportedLocales) {
            print('supportedLocales $supportedLocales $locale');
            if (supportedLocales
                .map((e) => e.languageCode)
                .contains(locale?.languageCode)) {
              return locale;
            }

            return const Locale('zh');
          },
          locale: SettingsController.shared.locale,
          onGenerateTitle: (BuildContext context) =>
              AppLocalizations.of(context)!.appTitle,
          themeMode: SettingsController.shared.themeMode,
          navigatorKey: MyApp.materialKey,
          builder: EasyLoading.init(builder: BotToastInit()),
          onGenerateRoute: (RouteSettings routeSettings) {
            return MaterialPageRoute<void>(
              settings: routeSettings,
              builder: (BuildContext context) {
                if (!lsappInitialized) {
                  SystemChrome.setSystemUIOverlayStyle(
                      SystemUiOverlayStyle.dark);
                  init(context);
                }

                switch (routeSettings.name) {
                  case LoginView.routeName:
                    return const LoginView();
                  case DriverInfoView.routeName:
                    return const DriverInfoView();
                  case DriverInfoEntryView.routeName:
                    return const DriverInfoEntryView();
                  case QuoteCenterView.routeName:
                    return const QuoteCenterView();
                  case QuoteDetailView.routeName:
                    return const QuoteDetailView();
                  case TruckManagementView.routeName:
                    return const TruckManagementView();
                  case TruckLicenseEntryView.routeName:
                    return const TruckLicenseEntryView();
                  case TruckInfoEntryView.routeName:
                    return const TruckInfoEntryView();
                  case BankManagementView.routeName:
                    return const BankManagementView();
                  case BankInfoEntryView.routeName:
                    return const BankInfoEntryView();
                  case OrderDetailView.routeName:
                    return const OrderDetailView();
                  case NotificationListView.routeName:
                    return const NotificationListView();
                  case OrderLogsView.routeName:
                    return const OrderLogsView();
                  case ActivityReportView.routeName:
                    return const ActivityReportView();
                  default:
                    return const HomeTabsView();
                }
              },
            );
          },
        );
      },
    );
  }

  static Future<void> init(BuildContext context) async {
    lsappInitialized = true;
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    SettingsController.shared.loadSettings();
    User.shared.init();
    debugPrint('== ${appLoc(context).localeName}');
    await LSApp.shared.init(context, appLoc(context).localeName,
        usingLSAppService: false,
        noConnectivityCallback: () {
          lsappInitialized = false;
        },
        remoteConfigCallback: (remoteConfig) async {
          if (remoteConfig == null) {
            showSnackBar(context,
                text: AppLocalizations.of(context)!.networkError,
                type: SnackType.error);
          } else {
            await SettingsController.shared.updateConfig(remoteConfig);
          }
        },
        forceUsingFirebase: true,
        messagingTokenCallback: (token) {
          debugPrint('messagingTokenCallback $token');
          User.shared.fcmToken = token;
          User.shared.registerFcmToken();
        },
        messagingPermissionDenied: (settings) async {
          debugPrint('messagingPermissionDenied');

          // final ret = await showAppDialog(
          //     context,
          //     appLoc(context).notificationPermissionRequiredMessage,
          //     [
          //       dialogButton(context, appLoc(context).openSettings,
          //           DialogButtonType.primary)
          //     ],
          //     title: appLoc(context).notificationPermissionRequiredTitle);
          // if (ret == DialogButtonType.primary) {
          //   openAppSettings();
          // }
        },
        fcmMessaging: (message, src) {
          debugPrint(
              '===> fcmMessaging $src ${message.data} ${message.notification?.title}');
          notif.Notification.shared.update();
          User.shared.updateAccountInfo();
          final routeName = message.data['routeName'] as String?;
          final arguments = message.data['arguments'];
          final type = message.data['type'];
          if (type == 'orderInvalidated' && src == MessagingSource.appOpen) {
            Future.delayed(Duration(milliseconds: 500), () {
              HomeTabsView.eventBus.fire(ChangeTabEvent(1));
              OrderView.eventBus.fire(OrderUpdateEvent());
            });
          } else if (routeName != null &&
              routeName.isNotEmpty &&
              src == MessagingSource.appOpen) {
            Navigator.of(context).pushNamed(routeName, arguments: arguments);
          } else if (src == MessagingSource.foreground &&
              message.notification?.body != null) {
            showSnackBar(context, text: message.notification!.body!);
          }
        },
        showSnackBarImpl: _showSnackBar);

    Messaging.clearAllNotifications();
  }

  static void _showSnackBar(SnackType type,
      {String text = '', Widget Function(TextStyle)? textWidget}) {
    Color foregroundColor = Colors.white;
    Icon leading = Icon(Icons.info_outline, color: foregroundColor);
    if (type == SnackType.success) {
      leading = Icon(Icons.check_circle_outline, color: foregroundColor);
    } else if (type == SnackType.error) {
      leading = Icon(Icons.cancel_outlined, color: foregroundColor);
    }

    BotToast.showCustomNotification(
        duration: const Duration(seconds: 3),
        enableKeyboardSafeArea: true,
        toastBuilder: (cancel) {
          return Card(
            color: Colors.black87,
            shape: RoundedRectangleBorder(
              side: const BorderSide(color: Colors.white24, width: 0.5),
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.symmetric(horizontal: 24),
            child: ListTile(
                dense: true,
                minLeadingWidth: 0,
                horizontalTitleGap: 8,
                leading: leading,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                title: Text(text,
                    style: TextStyle(
                        color: foregroundColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w300))),
          );
        },
        onlyOne: true,
        crossPage: true);
  }
}
