const { Timestamp } = require('firebase-admin/firestore');
const { HttpsError } = require('firebase-functions/https');
const fetch = require('node-fetch');
const { sendNotification, uploadImageToLark } = require('./utils.js');

const destinationOriginMap = {
  '霍尔果斯': 'Horgos Kazakhstan'
}

async function routeDistance(origin, destination) {
  try {
    const response = await fetch('https://routes.googleapis.com/directions/v2:computeRoutes', {
      method: 'post',
      body: JSON.stringify({
        'origin': {
          'address': origin
        },
        'destination': {
          'address': destination
        }
      }),
      headers: {
        'X-Goog-Api-Key': process.env.GOOGLE_MAPS_API_KEY,
        'X-Goog-FieldMask': 'routes.distanceMeters',
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    return data.routes[0].distanceMeters;
  } catch (e) {
    console.log(e);
    return null;
  }
}

async function updateStatus(firestore, id) {
  const larkClient = require('./lark-client.js');
  const fields = await larkClient.searchRecord(larkClient.orderTableConfig, id)
  const orderId = fields['微信小程序订单ID'][0].text
  const account = fields['下单账户'][0].text
  const status = fields['当前状态']['value'][0].text
  const origin = fields['起始地']
  const pickupAddress = fields['取货详细地址'] && fields['取货详细地址'][0].text
  const expectedLoadingDate = fields['期望装货时间']
  const destinationCountry = fields['目的国家']
  const destinationCity = fields['目的城市(wx)'] && fields['目的城市(wx)'][0].text
  const destinationAddress = fields['卸货详细地址'] && fields['卸货详细地址'][0].text
  const type = fields['车辆类型']
  const volumes = fields['车载体积']
  const tonnage = fields['车载吨位']
  const length = fields['车辆长度']
  const position = fields['装载位数']
  const goods = fields['货物种类'] && fields['货物种类'][0].text
  const targetPrice = fields['目标价格']
  const platformPrice = fields['马士拿报价']
  const actualCost = fields['配送费用']
  const driverId = fields['司机编号'] != null ? fields['司机编号'].value[0].text : null;
  const uid = fields['司机AppID'] != null ? fields['司机AppID'].value[0].text : null;

  const orderRef = firestore.collection('orders').doc(orderId);
  const exists = (await orderRef.get()).exists;
  if (!exists) {
    await orderRef.set({
      createdAt: Timestamp.now()
    });
  }
  await orderRef.set({
    orderId,
    recordId: id,
    account,
    status,
    origin,
    pickupAddress,
    expectedLoadingDate: Timestamp.fromMillis(expectedLoadingDate),
    destinationCountry,
    destinationCity,
    destinationAddress,
    type,
    volumes,
    tonnage,
    length,
    position,
    goods,
    targetPrice,
    driverId,
    actualCost,
    platformPrice,
    uid,
    updatedAt: Timestamp.now()
  }, { merge: true });

  const doc = (await orderRef.get()).data();
  if (doc.distance == null) {
    const distance = await routeDistance(destinationOriginMap[origin], `${destinationCountry}${destinationCity}`);
    if (distance != null) {
      await orderRef.set({
        distance
      }, { merge: true });
    }
  }

  await firestore.collection('orderRecords').add({
    orderId,
    status,
    account,
    createdAt: Timestamp.now()
  });

  if (status == '待确认车辆') {
    if (driverId != null) {
      const ref = firestore.collection('orderNegotiations').where('orderId', '==', orderId).where('driverId', '!=', driverId);
      await groupSendNotifications(firestore, ref, 'orderInvalidated');
    }
  } else if (status == '已取消') {
    const ref = firestore.collection('orderNegotiations').where('orderId', '==', orderId);
    await groupSendNotifications(firestore, ref, 'orderCancelled');
  } else if (status == '待装货' && uid != null) {
    await sendNotification(firestore, [uid], 'orderContracted', 'order_detail', orderId);
  } else if (status == '运输中' && uid != null) {
    await sendNotification(firestore, [uid], 'orderQueueCompleted', 'order_detail', orderId);
  } else if (status == '已完成' && uid != null) {
    await sendNotification(firestore, [uid], 'orderCompleted', 'order_detail', orderId);
  }

  return { code: 0, message: 'OK' };
}

async function groupSendNotifications(firestore, ref, type) {
  const docs = await ref.get();
  const uids = new Set();
  docs.docs.forEach(doc => {
    uids.add(doc.data()['uid']);
  });
  if (uids.size > 0) {
    console.log(`send ${type} notification to ${[...uids]}`);
    await sendNotification(firestore, [...uids], type);
  }
}

async function updatePrice(firestore, id) {
  const larkClient = require('./lark-client.js');
  const fields = await larkClient.searchRecord(larkClient.orderTableConfig, id)
  const orderId = fields['微信小程序订单ID'][0].text
  const targetPrice = fields['目标价格']
  const platformPrice = fields['马士拿报价']
  const account = fields['下单账户'][0].text
  const status = fields['当前状态']['value'][0].text

  const orderRef = firestore.collection('orders').doc(orderId);
  const orderDoc = await orderRef.get();
  const exists = orderDoc.exists;
  if (!exists) {
    return { code: -1, message: `Invalid orderId ${orderId}` };
  }
  await orderRef.set({
    targetPrice,
    platformPrice,
    updatedAt: Timestamp.now()
  }, { merge: true });

  await firestore.collection('orderRecords').add({
    orderId,
    status,
    account,
    targetPrice,
    platformPrice,
    createdAt: Timestamp.now()
  });

  // send notifications
  const negotiations = await firestore.collection('orderNegotiations').where('orderId', '==', orderId).get();
  const uids = new Set();
  negotiations.docs.forEach(doc => {
    uids.add(doc.data()['uid']);
  });

  await sendNotification(firestore, [...uids], 'orderPriceChanged', 'order_detail', orderId)

  return { code: 0, message: 'OK' };
}

async function createOrderNegotiation(firestore, request) {
  const { data: { orderId, orderRecordId, targetPrice, platformPrice, quotation, truckId, driverId, position } } = request;
  const uid = request.auth?.uid

  if (!uid || !orderId || !orderRecordId || targetPrice == null || platformPrice == null || quotation == null || !truckId || !driverId) {
    return new HttpsError('invalid-argument', 'Bad Request')
  }

  try {
    await addDriverLocation(firestore, uid, orderId, position);

    const ref = firestore.collection('orderNegotiations');
    const added = await ref.add({
      uid,
      orderId,
      orderRecordId,
      targetPrice,
      platformPrice,
      quotation,
      truckId,
      driverId,
      createdAt: Timestamp.now()
    });

    const larkClient = require('./lark-client.js');
    const id = added.id;
    let fields = {
      '订单编号': orderId,
      '订单价格': targetPrice,
      '马士拿报价': platformPrice,
      '司机报价': quotation,
      '车辆编号': truckId,
      '司机编号': driverId,
      '用户编号': uid,
      'Firebase记录编号': id
    };
    const res = await larkClient.createRecord(larkClient.orderNegotiationTableConfig, fields)
    if (res) {
      const { data: { record: { record_id } } } = res;
      await ref.doc(id).update({
        'recordId': record_id,
        'sync_at': Timestamp.now()
      })

      const order = (await firestore.collection('orders').doc(orderId).get()).data();
      // do not wait lark update
      order['status'] = '匹配中';
      return { order };
    } else {
      return new HttpsError('internal', 'Sync OrderNegotiation Error')
    }
  } catch (err) {
    console.error(err);
    return new HttpsError('internal', 'CreateOrderNegotiation Error')
  }
}

async function addDriverLocation(firestore, uid, orderId, position) {
  if (position != null) {
    const positionRef = firestore.collection('driverLocation');
    await positionRef.add({
      uid,
      orderId,
      position,
      createdAt: Timestamp.now()
    });
  }
}

async function uploadOrderAttachment(storagePath) {
  let attachment;
  const larkClient = require('./lark-client.js');
  if (storagePath?.length > 0) {
    // upload photo to lark
    attachment = await uploadImageToLark(storagePath, larkClient.orderTableConfig, larkClient);
  }
  return attachment;
}

async function updateOrderAndSyncLark(firestore, request, dbUpdate, fields, nextStatus) {
  const { data: { orderId, recordId, position } } = request;
  const uid = request.auth?.uid

  if (!uid || !orderId) {
    return new HttpsError('invalid-argument', 'Bad Request')
  }

  try {
    await addDriverLocation(firestore, uid, orderId, position);

    const ref = firestore.collection('orders').doc(orderId);
    await ref.update({
      ...dbUpdate,
      updatedAt: Timestamp.now()
    });

    const larkClient = require('./lark-client.js');
    const res = await larkClient.updateRecord(larkClient.orderTableConfig, recordId, fields)
    if (res) {
      const order = (await firestore.collection('orders').doc(orderId).get()).data();
      if (nextStatus) {
        // do not wait lark update
        order['status'] = nextStatus;
      } else {
        // TODO: add orderRecords if needed
      }
      return { order };
    } else {
      return new HttpsError('internal', 'updateOrderAndSyncLark Error')
    }
  } catch (err) {
    console.error(err);
    return new HttpsError('internal', 'updateOrderAndSyncLark Error')
  }
}

module.exports = {
  updateStatus,
  updatePrice,
  createOrderNegotiation,
  updateOrderAndSyncLark,
  uploadOrderAttachment
}